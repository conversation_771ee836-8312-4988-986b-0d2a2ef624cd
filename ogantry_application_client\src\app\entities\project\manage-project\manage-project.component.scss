@import '/src/assets/sass/components/variables.bootstrap';
@import 'primeicons/primeicons.css';

#manageProject {
  ::ng-deep .p-dropdown .p-dropdown-trigger {
    width: 1.2rem;
  }
  ::ng-deep .p-dropdown {
    width: 100%;
    border: 1px solid #4b3f72 !important;
  }

  ::ng-deep .p-dropdown .p-inputtext {
    border: 0 !important;
  }

  ::ng-deep .p-dropdown.p-focus {
    box-shadow: none !important;
  }

  ::ng-deep .p-inputtext {
    width: 100%;
    border: none !important;
  }

  ::ng-deep .cal-drop.p-element.p-dropdown {
    width: 100%;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  ::ng-deep .p-calendar .p-inputtext {
    width: 100%;
    height: inherit !important;
    border-radius: 3px;
    border-right: 0px !important;
    background-color: none !important;
    // display: none  !important;
  }

  ::ng-deep .p-dropdown-label {
    padding-right: 0rem !important;
    line-height: inherit !important;
  }

  .action-width {
    width: 28%;
  }

  .header-width-date {
    width: 30%;
  }

  .column-action-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .filter-header-width {
    width: 22%;
  }

  .filter-header-status {
    width: 18%;
  }

  .icon-background {
    background-color: #4b3f72 !important;
    padding: 0.2rem;
  }

  .icon-background-white {
    background-color: white;
    border: none;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
    background: #e9ecef;
    border-color: transparent;
    color: #495057;
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight) {
    border-radius: 50%;
    background-color: #f4f5f8;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page {
    font-size: 14px;
    &.p-highlight {
      background: #4b3f72;
      border-color: #e3f2fd;
      color: #ffffff;
      border-radius: 50%;
      pointer-events: none;
    }
  }

  ::ng-deep .p-datatable .p-datatable-thead tr > th:first-child,
  ::ng-deep .p-datatable .p-datatable-tbody tr > td:first-child {
    padding-left: 1rem;
  }

  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    height: 45px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    border: 1px solid #edeff3;
    background-color: #ecedf6 !important;
    position: sticky;
    top: 0px;
    padding: 0.5rem 1rem;
    width: 120px;
    flex: auto;
  }

  ::ng-deep .p-datatable-table {
    .customer-width,
    .project-width {
      width: 11% !important;
    }

    .duration-width {
      width: 7% !important;
    }

    .filter-header-status {
      width: 9% !important;
    }

    .tags-width {
      width: 11% !important;
    }
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 1rem;
    height: 40px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 120px;
    flex: auto;
  }

  ::ng-deep .pi-icon .pi {
    font-size: 0.5rem;
  }

  ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
    fill: white !important;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight,
  .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column:focus {
    box-shadow: inset 0 0 0 0.2rem #4b3f72;
    outline: 0 none;
  }
  ::ng-deep .p-paginator .p-dropdown {
    width: 91px;
  }

  ::ng-deep .p-datatable-tbody tr:last-child > td {
    border-bottom: none;
  }

  ::ng-deep .p-paginator {
    display: flex !important;
    justify-content: flex-start !important;
    border: none;
    margin-top: 15px;

    .p-paginator-current {
      position: absolute;
      right: 0;
      color: #575962;
      font-size: 14px;
      letter-spacing: 0;
      font-weight: 500;
      cursor: default;
    }

    .p-paginator-rpp-options {
      margin-left: 20px;
      height: 37px;
      width: 100px;
      border-radius: 20px;
      background-color: #f4f5f8;
      border: none;
    }
    .p-dropdown-label.p-inputtext {
      display: flex;
      align-items: center;
      padding-left: 15px;
      font-size: 14px;
    }

    .p-dropdown-trigger-icon {
      padding-right: 20px;
    }
  }

  .search-tag-wrapper {
    padding: 0rem;
    width: 100%;
  }

  ::ng-deep .search-tag-wrapper {
    .p-treeselect {
      width: 100% !important;
      max-width: 300px;
      border: 1px solid #4b3f72 !important;
      border-radius: 5px !important;
    }
  }

  ::ng-deep .p-treeselect-panel {
    .p-element {
      .p-tree {
        min-width: 250px !important;
        max-height: 50vh !important;
        overflow-y: auto;
        top: 200px !important;
        position: fixed;
        z-index: 1004 !important;
        box-shadow: 0 2px 4px -1px rgb(0 0 0 / 20%), 0 4px 5px 0 rgb(0 0 0 / 14%), 0 1px 10px 0 rgb(0 0 0 / 12%);
        border-radius: 3px;
      }
    }
  }

  ::ng-deep {
    .p-treeselect {
      width: 136px;
    }
  }

  ::ng-deep {
    span.tag-count p-badge span {
      background-color: #4b3f72;
    }
    .p-datatable-table {
      position: relative;
    }
  }

  @media (max-width: 1024px) {
    ::ng-deep .p-datatable table {
      display: block;
      overflow: auto;
      white-space: nowrap;
    }
  }

  @media (max-width: 500px) {
    ::ng-deep .p-paginator-current {
      right: 32px;
      bottom: 10px;
    }

    ::ng-deep .p-paginator-rpp-options {
      margin-top: 10px;
    }
  }

  ::ng-deep .p-inputtext:enabled:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: none;
    border-color: none;
  }

  ::ng-deep .p-inputtext:enabled:hover {
    border-color: #4b3f72;
  }
  .badge {
    height: 20px;
    width: 56px;
    color: #dc2f44;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
    text-align: center;
  }

  .badge-forecast {
    color: #109aa1 !important;
  }

  .badge-draft {
    color: #dc2f44;
  }

  .badge-booked {
    color: #f3ef10;
  }

  .badge-completed {
    color: #0f6116;
  }

  .center-align {
    text-align: center !important;
  }

  .show-pointer {
    cursor: pointer;
    color: #4b3f72 !important;
    text-decoration: underline;
  }

  ::ng-deep .p-datatable .p-datatable-loading-overlay {
    top: 80px;
    background-color: transparent;
  }

  // ::ng-deep .p-datatable-customers{
  //   display: block;
  //   overflow: auto;
  //   white-space: nowrap;
  // }

  ::ng-deep .p-calendar {
    // display:none !important;
    width: 80% !important;
    display: flex;
    margin-right: 0.2rem;
  }

  .help-icon {
    color: #b5b5c3;
    cursor: pointer;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  ::ng-deep .p-calendar .p-datepicker {
    position: absolute;
    width: 400px;
  }
  ::ng-deep .p-datatable-responsive-scroll > .p-datatable-wrapper {
    overflow-x: initial !important;
    height: auto !important;
    overflow-y: auto !important;
  }

  ::ng-deep .confirm-dialog .p-dialog {
    width: 30vw;
  }

  ::ng-deep .p-input-icon-right > em:last-of-type {
    color: #8a9191;
    font-size: 0.5rem;
    right: 0.5rem;
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
  }

  ::ng-deep .p-input-icon-right {
    border: 1px solid #4b3f72;
    border-radius: 4px;
    background-color: white;
  }

  ::ng-deep .p-input-icon-right:hover {
    outline: 0 none;
    outline-offset: 0;

    border: 1px solid;
    border-color: #4b3f72;
  }

  ::ng-deep .p-multiselect.p-focus {
    box-shadow: 0 0 0 0.1rem #4b3f72 !important;
    width: 100%;
    height: 100%;
  }

  ::ng-deep .search-status-wrapper .p-multiselect {
    width: 100%;
    padding: 0rem;
    border: 1px solid #4b3f72 !important;
    background-color: #ffffff !important;
    border-radius: 5px !important;
    height: 36px !important;
    min-height: 0px !important;
  }

  ::ng-deep .p-multiselect-header .p-multiselect-close,
  ::ng-deep .p-multiselect-filter-container {
    display: none;
  }

  ::ng-deep .select-column-wrapper .dropdown .p-dropdown,
  ::ng-deep .select-column-wrapper .p-multiselect {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 30px !important;
  }

  ::ng-deep .select-column-wrapper .dropdown .p-dropdown,
  ::ng-deep .select-column-wrapper .dropdown .p-dropdown .p-focus {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    box-shadow: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
    padding: 1.2rem;
  }

  ::ng-deep .select-column-wrapper .dropdown .p-dropdown,
  ::ng-deep .select-column-wrapper .p-multiselect {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
    padding: 1.2rem;
    max-width: 250px;
  }

  ::ng-deep .select-column-wrapper.dropdown .p-dropdown .p-dropdown-label,
  ::ng-deep .select-column-wrapper .p-multiselect .p-multiselect-label {
    color: #000000;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: -0.32px;
    line-height: 25px;
    padding-top: 0.5rem;
    padding-left: 1rem;
  }

  ::ng-deep .select-column-wrapper .dropdown .p-dropdown .p-dropdown-label {
    color: #000000;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: -0.32px;
    line-height: 25px;
  }

  ::ng-deep .p-multiselect-header {
    width: 20%;
    background-color: white;
  }

  ::ng-deep .p-multiselect-header::after {
    display: block;
    content: 'All';
  }

  ::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
    border-color: #827da0 !important;
    background: #827da0 !important;
  }

  ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #827da0;
  }

  ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box .p-highlight:hover {
    border-color: #827da0 !important;
    background: #827da0 !important;
  }

  ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
    border-color: #dcdcdd;
    background: #dcdcdd;
    font-family: Poppins !important;
  }

  ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus {
    box-shadow: none;
    font-family: Poppins !important;
  }

  ::ng-deep .p-multiselect.p-multiselect-chip .p-multiselect-token {
    background: #dcdcdd;
  }
  .text-number-right {
    text-align: left;
  }

  ::ng-deep .p-multiselect .p-multiselect-label.p-placeholder {
    color: #6c757d !important;
    font-size: 13px;
    font-weight: normal;
    background-color: #ffffff;
    line-height: 21px;
    padding: 0.5rem 0.5rem !important;
  }
  ::ng-deep .p-multiselect .p-multiselect-label {
    font-size: 13px;
    font-weight: normal;
    line-height: 21px;
  }
  ::ng-deep .p-multiselect .p-multiselect-trigger {
    background-color: #ffffff;
    border-radius: 10px;
  }

  ::ng-deep .p-multiselect-label-container {
    width: 10rem;
    border-radius: 10px;
  }

  ::ng-deep .p-inputgroup-addon {
    background-color: #f8f8ff !important;
    border-radius: 4px;
    border-color: #4b3f72;
    border-right: none;
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px;
  }

  ::ng-deep .p-inputgroup-cancel {
    background-color: #f8f8ff !important;
    border: 1px solid #4b3f72;
    border-radius: 4px;
    border-left: none;
    border-radius: 0 4px 4px 0;
    display: flex;
    align-items: center;

    .pi-times {
      font-size: 8px;
      padding-right: 3px;
      cursor: pointer;
    }
  }

  .share-icon {
    background-color: #4b3f72 !important;
    border-color: #4b3f72 !important;
    i,
    em {
      color: white;
    }
  }
  .button-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
::ng-deep .confirm-dialog .p-dialog {
  width: 24vw;
}

::ng-deep .confirm-dialog .p-dialog-title {
  color: #000000;
  font-family: Poppins;
  font-size: 16px !important;
  font-weight: 500 !important;
  letter-spacing: 0;
  line-height: 25px;
}
.delete-title {
  color: #050505;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.32px;
  line-height: 25px;
}

.action-icons a {
  margin: 0 3px;
}

::ng-deep .dialog-applied-tags {
  .p-dialog {
    width: 400px !important;
  }
}

::ng-deep .filter-dialog-manage-project {
  .p-component-overlay {
    background: none !important;
    animation: none !important;
  }

  .p-dialog {
    height: auto !important;
    max-height: calc(100vh - 90px) !important;
    width: 400px;
    top: 51px;

    .p-dialog-header {
      display: none;
    }

    .p-dialog-content {
      padding-top: 1rem;
    }

    .title {
      color: #757575;
      font-family: Poppins;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 18px;
    }

    .share-icon {
      background-color: #4b3f72 !important;
      border-color: #4b3f72 !important;

      i,
      em {
        color: white;
      }
    }

    .filter-body {
      display: flex;
      align-content: center;
      align-items: center;
    }

    .form-check {
      height: 40px;
      background-color: white;
      display: flex;
      align-items: center;
      width: 100%;

      .form-check-label,
      .form-check-label:hover {
        cursor: pointer;
        color: black;
        width: 75%;
        color: #000000;
        font-family: Poppins;
        font-size: 12px;
        letter-spacing: 0;
        line-height: 18px;
      }

      .div {
        width: 30%;
        display: flex;
        justify-content: flex-end;
      }
    }

    .filter-icons {
      display: flex;
      width: 100%;
      justify-content: flex-end;
    }
  }
}
::ng-deep .popup-column {
  z-index: 100 !important;
  position: absolute !important;
  right: 5px !important;
  top: 100px !important;
}
::ng-deep .plus-icon {
  svg {
    fill: $primary;
  }
}

::ne-deep .description-dialog .p-dialog {
  overflow: auto;
  max-height: 90vh !important;
  min-height: 30vh !important;
  width: 30% !important;
}

.description-dialog .description-text {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  background-color: white !important;
}

.tag-overflow {
  height: 90vvh !important;
  overflow-y: auto !important;
  padding-bottom: 2px !important;
}
.dynamic-text {
  max-width: 120px !important;
}

.date-filter {
  border-left: none !important;
  border-right: none !important;
  border-radius: 0 !important;
}
