import { FormControl } from '@angular/forms';
import { OGantryHttpResponse } from './../../../@shared/models/custom/http-response.model';
import { QueryFilter } from './../../client/client.model';
import { AuthService } from './../../../@auth/auth.service';
import { PermissionModules } from './../../../@shared/models/permission.enum';
import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, OnInit, ViewChild, SimpleChanges } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { ProjectService } from '@entities/project/project.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { LazyLoadEvent } from 'primeng/api';
import { MultiSelect } from 'primeng/multiselect';
import {
  FilterReport,
  IFilter,
  OpenPositionReport,
  QueryFilterParams,
  TableHeader,
  Group,
  CalendarPageViewConfigType,
  MONTH_NAMES,
  ValidatedMonthlyPositions,
  PdfColumnHeaderObj,
  SkillSetFilterObj
} from '../utilization.model';
import { UtilizationService } from '../utilization.service';
import { AppConstants } from '@shared/constants';
const height = 'calc((var(--fixed-content-height, 1vh) * 100) - 180px)';
const tableHeight = 'calc((var(--fixed-content-height, 1vh) * 96) - 180px)';
import {
  GlobalDetailSubCategory,
  GlobalDetailTaggingCategory,
  GlobalDetailTags,
  ISavedFilterList,
  SaveFilter,
  SubCategory,
  TagCategory
} from '@entities/administration/administration.model';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import moment from 'moment';
import { Employee, Employees, Position } from '@entities/project/project.model';
import { AdministrationService } from '@entities/administration/administration.service';
import * as _ from 'lodash';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { isArray, isString } from 'lodash';
import { HttpParams } from '@angular/common/http';
import { PNGTree, TreeNode, TreeViewStructure } from '@entities/administration/append-tags/tree-view-model';
import { setOptions, Notifications, MbscEventcalendarView, MbscEventcalendarOptions, MbscPopupOptions, MbscPopup, formatDate } from '@mobiscroll/angular';
import { ClientService } from '@entities/client/client.service';
import { MatDialog } from '@angular/material/dialog';
import { AppendTagsComponent } from '@entities/administration/append-tags/append-tags.component';
import { ColumnToggleService } from '@shared/services/column-toggle.service';
import { AddCommasToNumbersPipe } from '@shared/pipes/add-commas-to-numbers.pipe';
import { ComponentsType, FiledType } from '@shared/models/component-type-enum';
import { Debounce } from '@shared/decorators/debounce.decorator';
import { ExtendedFormComponent } from '@shared/components/extended-form/extended-form.component';
import { extendedField } from '@shared/models/extended-field.model';

setOptions({
  theme: 'ios',
  themeVariant: 'light'
});

const now = new Date();

interface ResorceType {
  id: number;
  name: string;
  customer: string;
  color: string;
}

interface ResourceEventType {
  start: string;
  end: string;
  title: string;
  resource: number;
  employeeFirstName: string;
  employeeLastName: string;
}

@Component({
  selector: 'app-staffed-position-report',
  templateUrl: './staffed-position-report.component.html',
  styleUrls: ['./staffed-position-report.component.scss'],
  providers: [DatePipe, Notifications],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StaffedPositionReportComponent extends SflBaseComponent implements OnInit, AfterViewInit {
  downloadButtonDisable = true;
  showPaginator = false;
  lastQueryFilter: any;
  exportButtonDisable = true;
  positionExportData: OpenPositionReport[] = [];
  page: number = 0;
  pageSize: number = 10; // Number of records per page
  totalRecords: number = 0; // Total records from the backend
  projectID: number;
  filedType = FiledType;
  openFilter = false;
  showPositionUpdateDateDialog = false;
  cardTitle = 'Positions Report';
  cardSubTitle = null;
  tableButtonActive = true;
  calendarButtonActive = false;
  buttons: ButtonParams[] = [
    {
      btnSvg: 'table-active',
      btnClass: 'btn-switcher btn-left',
      isActive: this.tableButtonActive,
      isSwitcherButton: true,
      viewType: 'table',
      action: this.switchToCalendarView.bind(this)
    },
    {
      btnSvg: 'calendar',
      btnClass: 'btn-switcher btn-right',
      isActive: this.calendarButtonActive,
      isSwitcherButton: true,
      viewType: 'calendar',
      action: this.switchToTableView.bind(this)
    }
  ];
  splitButtonDropDownOption = {
    action: this.openSideBar.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.openSaveFilterList();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSaveFilter();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilter();
        }
      }
    ]
  };
  exportButtons: ButtonParams[] = [
    {
      action: this.exportReport.bind(this)
    }
  ];
  showHideButton = [
    {
      action: this.showHideColumns.bind(this)
    }
  ];
  filterCardTitle = 'Filter';
  filterButtons: ButtonParams[] = [
    {
      btnSvg: 'filter-list',
      btnClass: 'btn-filter-icon',
      action: this.openSaveFilterList.bind(this)
    },
    {
      btnSvg: 'save',
      btnClass: 'btn btn-sm btn-icon btn-icon-light svg-icon svg-icon-md icon-background mr-2 filter-btn-wrapper',
      action: this.onSaveFilter.bind(this)
    },
    {
      btnClass: 'btn-close-icon',
      btnIcon: 'times',
      action: this.onClose.bind(this, true)
    }
  ];
  styleObj = {
    heading: {
      color: 'rgb(109 108 108)',
      fontFamily: 'Poppins',
      fontSize: '10px',
      fontWeight: '500',
      letterSpacing: '0',
      lineHeight: '16px'
    },
    subHeading: {
      color: '#242424',
      fontFamily: 'Poppins',
      fontSize: '12px',
      letterSpacing: '0',
      lineHeight: '18px'
    }
  };
  height = height;
  tableHeight = tableHeight;
  sidebarParams: SidebarParams<FilterReport>;
  showFilterListDialog = false;
  availableFilters = null;
  selectedFilter = null;
  @ViewChild('sidebarFilter', { static: true }) el: MatSidenav;
  csvCols = [];
  exportPdfColumns = [];
  exportReportData = [];
  excelExportReportData = [];
  excelHeaders = [];
  tableHeaders: TableHeader[] = [];
  loading = false;
  positionReportData: OpenPositionReport[] = [];
  dataFilter: IFilter = new IFilter();
  positionList = [];
  positionTypeList = [];
  projectList = [];
  clientGroup: Group[];
  projectGroup: Group[];
  tags = [];
  frozenCols = [];
  statuses = [];
  defaultStatuses: string[];
  dateError = false;
  dateRequired = false;
  projectStatus: any;
  resizeFlag = false;
  sortColumnFlag = false;
  sortFieldName: string = 'name';
  sortOrderNumber: number = 1;
  client = [];
  showBillRate = false;
  showClientFilter = false;
  groupByClient = false;
  groupByEmployee = false;
  groupByProject = false;
  groupByClientCalenderView: boolean;
  groupByEmployeeCalenderView: boolean;
  groupByProjectCalenderView: boolean;
  ClientName;
  selectedClientName = [];
  selectedProjectName = [];
  showProjectFilter = false;
  projectName;
  @ViewChild('multiSelectComp4') multiSelectComp4: MultiSelect;
  employeeList: Employee[] = [{ label: '-- None --', id: 0, value: '0' }];
  showEmployeeFilter = false;
  employeeName;
  selectedEmployeeName = [];
  employeeGroup: Group[];
  @ViewChild('multiSelectComp') multiSelectComp: MultiSelect;
  @ViewChild('multiSelectComp2') multiSelectComp2: MultiSelect;
  @ViewChild('multiSelectComp3') multiSelectComp3: MultiSelect;
  positionEdit = false;
  clonePosition: { [s: string]: OpenPositionReport } = {};
  editPositionObj: Position = new Position();
  skillSetList = [];
  positionMinDate = null;
  showPositionError = false;
  showStartDateError = false;
  showEndDateError = false;
  showHoursError = false;
  sharedFilters: QueryFilter[] = [];
  myFilters: QueryFilter[] = [];
  showSavedFilter = false;
  selectedFilterFormControl = new FormControl('');
  editFilterObj: QueryFilter;
  showNameError = false;
  showDeleteDialog = false;
  deleteFilterObj: QueryFilter;
  showShareDialog = false;
  shareFilterObj = null;
  showEditDialog = false;
  filteredFilters: ISavedFilterList;
  queryFilterId: number;
  duplicatedPositionObject: Position;
  editModeEnable = false;
  taggingTags = [];
  tagCategories: TagCategory[] = [];
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  tagSubCategory: SubCategory[] = [];
  globalDetailsTagSubCategory: GlobalDetailSubCategory;
  globalDetailsTag: GlobalDetailTags;
  groupedCategory: TreeViewStructure;
  selectedCategoriesTag: PNGTree[] = [];
  selectedTags = [];
  treeViewSelectedTags = [];
  finalTagsAlongWithTheCategory: string[] = [];
  showTagDialog = false;
  selectedTagToView;
  _pCols: string[] = [];
  selectedColumns: any;
  shifts: any[];
  obj: { children: any[] };
  showExportOptionDialog: boolean;
  showExportOptions: boolean;
  calBreakDown: string;
  defaultView: string = 'table';
  extendFields: any;
  extendFieldsObj: any;
  componentType = ComponentsType.Position;

  positionOpenOrAllList = [
    {
      key: 'Show Open Positions',
      value: true
    },
    {
      key: 'Show Staffed Positions',
      value: false
    },
    {
      key: 'Show All Positions',
      value: undefined
    }
  ];
  myResources = [];
  myEvents = [];
  preparingCalendarData = true;
  timelineType: CalendarPageViewConfigType = CalendarPageViewConfigType.year;
  timelineResolution: CalendarPageViewConfigType = CalendarPageViewConfigType.month;
  calendarViewType = CalendarPageViewConfigType;
  myView: MbscEventcalendarView = {
    timeline: {
      type: this.timelineType,
      size: 1,
      resolution: this.timelineResolution,
      eventList: true
    }
  };
  @ViewChild('popup', { static: false })
  tooltip!: MbscPopup;
  selectedPosition;
  currentEvent: any;
  popupOptions: MbscPopupOptions = {
    display: 'anchored',
    touchUi: false,
    showOverlay: false,
    contentPadding: false,
    closeOnOverlayClick: false,
    width: 350
  };
  status = '';
  buttonText = '';
  buttonType = '';
  bgColor = '';
  info = '';
  time = '';
  reason = '';
  location = '';
  anchor: HTMLElement | undefined;
  timer: any;
  redColor = '#ffc857';
  greenColor = '#4b99a2';
  calendarOptions: MbscEventcalendarOptions = {
    resources: this.myResources,
    view: {
      timeline: {
        type: 'day',
        startDay: 1,
        endDay: 5,
        startTime: '08:00',
        endTime: '16:00',
        allDay: false
      }
    },
    clickToCreate: false,
    dragToCreate: false,
    showEventTooltip: false,
    onEventHoverIn: (args, inst) => {
      const event: any = args.event;
      const resource: any = this.positionReportData.find((dr) => dr.position?.id === event.resource);
      this.bindPositionToSelectedPosition(resource);
      const time = formatDate('hh:mm A', new Date(event.start)) + ' - ' + formatDate('hh:mm A', new Date(event.end));
      this.cdf.detectChanges();
      this.currentEvent = event;
      this.bgColor = resource?.position?.employee?.first_name ? this.greenColor : '#d3a75d';
      clearTimeout(this.timer);
      this.timer = null;
      this.anchor = args.domEvent.target;
      this.tooltip.open();
      setTimeout(() => {
        if (this.timelineResolution === 'week' || this.timelineResolution === 'month') {
          document.getElementsByClassName('md-tooltip')[1].classList.add('week-month-popup-wrapper');
        }
      });
    },
    onEventHoverOut: () => {
      if (!this.timer) {
        this.timer = setTimeout(() => {
          this.tooltip.close();
        }, 200);
      }
    },
    onEventClick: () => {
      this.tooltip.open();
    }
  };
  loadingReport: boolean;
  isShowHideColumns: boolean = false;
  selectedColumnsArray: any = [];
  showApplyMsg = true;
  updatingButton = false;
  unableToDownloadPdf = false;
  updateExtendFiled: string;
  showUpdateExtendFiledDialog = false;
  positionObj: Position;
  projectId: number;
  positionSetupForm: OpenPositionReport;
  selectedSkillSetObj: SkillSetFilterObj;
  showHyperlinkNavigationDialog = false;
  linkValue = '';
  fieldDetail: extendedField;
  @ViewChild('childForm') extendedFieldFormComponent: ExtendedFormComponent;

  constructor(
    private readonly datePipe: DatePipe,
    private readonly utilizationService: UtilizationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly projectService: ProjectService,
    private readonly layoutConfigService: LayoutConfigService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly adminService: AdministrationService,
    private readonly authService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly clientService: ClientService,
    private readonly dialog: MatDialog,
    private readonly columnToggle: ColumnToggleService,
    readonly commaNumberPipe: AddCommasToNumbersPipe
  ) {
    super();
  }
  ngOnChanges(changes: SimpleChanges): void {
    throw new Error('Method not implemented.');
  }

  bindPositionToSelectedPosition(resource) {
    this.selectedPosition = resource;
    this.cdf.detectChanges();
  }

  async ngOnInit(): Promise<void> {
    if (window.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
    if (this.cacheFilter.getCacheFilters('positions-report')) {
      this.dataFilter = this.cacheFilter.getCacheFilters('positions-report');
      this.page = this.dataFilter.offset || 0;
      this.pageSize = this.dataFilter.limit || 10;
    } else {
      this.dataFilter.open_positions = false;
    }

    this.calBreakDown = localStorage.getItem('calendarBreakDown');
    if (this.calBreakDown) {
      this.pageResolutionHandler(this.calBreakDown);
    }

    if (this.dataFilter.filterView == 'table') {
      this.calendarButtonActive = false;
      this.tableButtonActive = true;
      this.defaultView = 'table';
    } else if (this.dataFilter.filterView == 'calendar') {
      this.defaultView = 'calendar';
      this.calendarButtonActive = true;
      this.tableButtonActive = false;
    }

    this.buttons = [
      {
        btnSvg: 'download-wt',
        btnClass: 'btn-filter-icon download',
        action: this.openExportOptionList.bind(this)
      },
      {
        btnSvg: this.tableButtonActive ? 'table-active' : 'table',
        btnClass: 'btn-switcher btn-left',
        isActive: this.tableButtonActive,
        isSwitcherButton: true,
        viewType: 'table',
        action: this.switchToCalendarView.bind(this)
      },
      {
        btnSvg: this.calendarButtonActive ? 'calendar-active' : 'calendar',
        btnClass: 'btn-switcher btn-right',
        isActive: this.calendarButtonActive,
        isSwitcherButton: true,
        viewType: 'calendar',
        action: this.switchToTableView.bind(this)
      }
    ];

    this.showBillRate = await this.authService.isPermittedAction([PermissionModules.VIEW_BILL_RATE]);
    this.cdf.detectChanges();

    this.frozenCols = [
      {
        dataKey: 'customer_name',
        title: 'Client Name',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'project_name',
        title: 'Project Name',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'project_status',
        title: 'Project Status',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'position_name',
        title: 'Position Name',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'employee_full_name',
        title: 'Employee',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'employee_type',
        title: 'Skill Set',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'bill_rate',
        title: 'Bill Rate',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'daily_billable_hours',
        title: 'Hours (Daily)',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'weekly_billable_hours',
        title: 'Hours (Weekly)',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'cost',
        title: 'Hourly Cost',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'billable_hours',
        title: 'Total Projected Hours',
        sort: true,
        bydefaultSelected: false
      },
      {
        dataKey: 'actual_hours',
        title: 'Total Actual Hours',
        sort: true,
        bydefaultSelected: false
      },
      {
        dataKey: 'variance',
        title: 'Total Variance',
        sort: true,
        bydefaultSelected: false
      },
      { dataKey: 'tags', title: 'Tags', bydefaultSelected: true },
      {
        dataKey: 'start_date',
        title: 'Start Date',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'end_date',
        title: 'End Date',
        sort: true,
        bydefaultSelected: true
      },
      {
        dataKey: 'monthly_cost',
        title: 'Cost',
        bydefaultSelected: false
      },
      {
        dataKey: 'monthly_hours',
        title: 'Projected Hours',
        bydefaultSelected: false
      },
      {
        dataKey: 'monthly_ActualHours',
        title: 'Actual Hours',
        bydefaultSelected: false
      },
      {
        dataKey: 'monthly_Variance',
        title: 'Variance',
        bydefaultSelected: false
      }
    ];

    const IsPermitted = await this.authService.isPermittedAction([PermissionModules.VIEW_BILL_RATE]);
    if (IsPermitted) {
      this.frozenCols = [...this.frozenCols, ...this.addFinicalColumn()];
    }
    this.selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.openPositionReport
      ? JSON.parse(localStorage.getItem('selectedColumnsArray'))['openPositionReport']
      : this.frozenCols.filter((col) => col.bydefaultSelected === true);
    this._pCols = this.selectedColumns.map((f) => f.dataKey);

    this.setExportHeaders();
    this.getPositionList();
    this.getEmployeeList();
    this.getEmployeeGroup();
    this.getProjectList();
    this.getStoredFilters();
    this.getClientGroup();
    this.getProjectGroup();
    this.getProjectStatus();
    this.getClient();
    this.applyAllFilter();
    this.getSkillSets();
    this.getCategoryMasterData();
    this.getGlobalDetailsCategory();
    this.handelManageProject();
    this.clientService.saveFilterData$.subscribe((res) => {
      res === true ? this.filterReport() : '';
    });

    this.utilizationService.showTagsForEditPosition.subscribe((res) => {
      if (res) {
        this.loadReport();
        this.cdf.detectChanges();
        this.layoutUtilsService.showActionNotification('Position updated successfully', AlertType.Success);
      }
    });
  }

  showHideColumns(type) {
    if (type == 'showColumns') {
      this.isShowHideColumns = true;
    } else {
      this.isShowHideColumns = false;
    }
  }

  onSelectColumsChange(event) {
    if (event) {
      this.columnToggle.setSelectedColumns(event.value, 'openPositionReport');
      this.selectedColumns = event.value;
      this._pCols = event.value.map((f) => f.dataKey);
      this.prepareHeaders();
    }
    this.isShowHideColumns = !this.isShowHideColumns;
    this.cdf.detectChanges();
  }

  getCategoryMasterData() {
    this.tagCategories = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.tagCategories = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.adminService.setTagCategories(globalDetail[0].global_detail);
              this.getTagSubCategories();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getTagSubCategories() {
    this.tagSubCategory = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTagSubCategory = globalDetail[0];
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
              this.adminService.setTagSubCategories(globalDetail[0].global_detail);
              this.getGlobalDetailTags();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  onAction(button: ButtonParams) {
    if (button.action) {
      button.action();
      this.dataFilter.filterView = button.viewType;
      this.defaultView = button.viewType;
      this.cacheFilter.setCacheFilters(this.dataFilter, 'positions-report');
    } else {
      this.router.navigate([button.redirectPath]);
    }
  }

  getGlobalDetailTags() {
    this.taggingTags = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTags('TagManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
              this.globalDetailsTag = globalDetail[0];
              this.taggingTags = globalDetail[0].global_detail.extended_fields.tags;
              this.adminService.setTags(globalDetail[0].global_detail);
              this.combineCategoryAndSubCategory();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  combineCategoryAndSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
    this.injectTagsToRespectiveCategoryOrSubCategory();
    this.initGroupingCategoryTags();
  }

  injectTagsToRespectiveCategoryOrSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      for (const tag of this.taggingTags) {
        if (tag.tagCategory === category.id) {
          const subCateIndex = category.subTagCategory.findIndex((subCate) => subCate.id === tag.subTagCategory);
          if (subCateIndex !== -1) {
            category.subTagCategory[subCateIndex]['tags'].push(tag);
          } else {
            category.tags.push(tag);
          }
        }
      }
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
  }

  onFilterChangePreapareSelectedTreeNodes() {
    if (this.dataFilter.tags?.length) {
      this.selectedTags = [];
      const tagsWithCategory = this.dataFilter.tags.split(',');
      for (const tag of tagsWithCategory) {
        const pngTreeItem: PNGTree = new PNGTree();
        const tagLength = tag.split('__');
        pngTreeItem.collapsedIcon = 'pi-chevron-right';
        pngTreeItem.expandedIcon = 'pi-chevron-down';
        pngTreeItem.label = this.getExtractedTags(tag);
        pngTreeItem.expanded = true;
        pngTreeItem.partialSelected = false;
        pngTreeItem.parent = {
          label: this.getExtractedTagsParentCategory(tag),
          children: [],
          collapsedIcon: 'pi-chevron-right',
          expandedIcon: 'pi-chevron-down',
          expanded: true,
          partialSelected: true,
          selectable: false,
          parent:
            tagLength?.length > 2
              ? {
                  label: this.getExtractedTagsParentCategory(tag),
                  children: [],
                  partialSelected: true,
                  selectable: false,
                  collapsedIcon: 'pi-chevron-right',
                  expandedIcon: 'pi-chevron-down',
                  expanded: true,
                  parent: undefined
                }
              : undefined
        };
        if (this.groupedCategory) {
          for (const parent of this.groupedCategory.data) {
            for (const children of parent.children) {
              for (const children_data of children.children) {
                if (children_data.label === pngTreeItem.label) {
                  this.selectedTags.push(children_data);
                }
              }
            }
          }
        }
      }
      this.cdf.detectChanges();
    }
  }

  initGroupingCategoryTags() {
    this.groupedCategory = { data: [] };
    for (const [index, category] of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory.entries()) {
      let dataCollection: TreeNode = new TreeNode();
      dataCollection.label = category.name;
      dataCollection.selectable = false;
      dataCollection.collapsedIcon = 'pi-chevron-right';
      dataCollection.expandedIcon = 'pi-chevron-down';
      dataCollection.expanded = true;
      if (category?.subTagCategory?.length) {
        for (const [subIndex, subCate] of category?.subTagCategory?.entries()) {
          dataCollection.children.push({
            label: subCate.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            children: [],
            selectable: false,
            expanded: true
          });
          if (subCate?.tags?.length) {
            for (const [tagIndex, tag] of subCate?.tags?.entries()) {
              dataCollection.children[subIndex]?.children?.push({
                label: tag.name,
                collapsedIcon: 'pi-chevron-right',
                expandedIcon: 'pi-chevron-down',
                expanded: true
              });
            }
          }
        }
      }
      if (category?.tags?.length) {
        for (const [tagIndex, tag] of category?.tags?.entries()) {
          dataCollection.children?.push({
            label: tag.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            expanded: true
          });
        }
      }
      this.groupedCategory.data.push(dataCollection);
    }
  }

  clearAppliedTagTag(clearThisTag) {
    let tags = this.dataFilter.tags.split(',');
    tags = tags.filter((t) => !t.includes(clearThisTag));
    this.dataFilter.tags = tags.toString();
    this.selectedTags = this.selectedTags.filter((t) => !t?.label?.includes(clearThisTag));
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: null },
      queryParamsHandling: 'merge'
    });
    this.doFilterData();
  }

  defaultFilters() {
    this.tags = [];
    this.showClientFilter = false;
    if (!this.dataFilter?.project_statuses) {
      this.dataFilter.project_statuses = this.defaultStatuses.toString();
      this.projectStatus = this.defaultStatuses;
      this.defaultView = 'table';
      this.dataFilter.filterView = 'table';
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.project_statuses,
        key: ['project_statuses']
      });
    }

    if (!this.dataFilter.start_date_lt && !this.dataFilter.end_date_gte && !this.dataFilter?.filterFromMangeEmployee) {
      this.dataFilter.rollingOption = 'Current plus 2 months';
      if (this.dataFilter.rollingOption) {
        this.tags.push({
          label: 'Rolling',
          value: this.dataFilter.rollingOption.toString(),
          key: ['start_date_lt', 'end_date_gte', 'rollingOption']
        });
      }
    }
    if (this.dataFilter?.filterFromMangeEmployee) {
      this.dateRangeCalculation();
      delete this.dataFilter.filterFromMangeEmployee;
      this.loadReport();
    }
    this.dateRangeCalculation();
    this.applyAllFilter();
  }

  getEmployeeGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'employees'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const employeeGrp = [];
        response?.data?.query_filters?.map((query) => {
          employeeGrp.push({
            label: query.query_filter.name,
            value: {
              name: query.query_filter.name,
              value: query.query_filter.query_string
            }
          });
        });
        this.employeeGroup = employeeGrp;
        this.sortList(this.employeeGroup);
        this.cdf.detectChanges();
      })
    );
  }

  getEmployeeList() {
    const queryFilter = {
      order_by: 'asc:first_name',
      employee_status: 'active'
    };
    this.subscriptionManager.add(
      this.projectService.getEmployeeList(queryFilter).subscribe((res: OGantryHttpResponse<Employees>) => {
        res?.data?.employees?.forEach((e) => {
          this.employeeList.push({
            label: e.employee?.name,
            id: e.employee.id,
            value: e.employee.id.toString()
          });
        });
        this.sortList(this.employeeList);
        if (this.dataFilter.value) {
          this.dataFilter.value = this.dataFilter.value;
          this.cdf.detectChanges();
        }
      })
    );
  }
  getClient() {
    this.subscriptionManager.add(
      this.utilizationService.getClientData().subscribe((res) => {
        const client = [];
        res?.body?.data?.customers?.map((c) => {
          client.push({
            label: c.customer.name,
            value: String(c.customer.id)
          });
        });
        this.client = client;
        this.sortList(this.client);
        this.cdf.detectChanges();
      })
    );
  }

  sortList(sortList) {
    if (this.sortList.length > 0) {
      sortList.sort((a, b) => {
        const fa = a?.label?.toLowerCase();
        const fb = b?.label?.toLowerCase();
        if (fa < fb) {
          return -1;
        }
        if (fa > fb) {
          return 1;
        }
        return 0;
      });
    }
  }

  sortColumn() {
    this.sortColumnFlag = true;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
  }

  ngAfterViewInit() {
    if (this.multiSelectComp) {
      this.multiSelectComp.options = this.statuses;
    }
    if (this.multiSelectComp2) {
      this.multiSelectComp2.options = this.client;
    }
    if (this.multiSelectComp3) {
      this.multiSelectComp3.options = this.projectList;
    }
    if (this.multiSelectComp4) {
      this.multiSelectComp4.options = this.employeeList;
    }

    if (this.el) {
      this.loading = false;
      this.loading$.next(false);
      this.sidebarParams = { template: this.el };
      this.sidebarParams.template.open();
      this.openFilter = true;
    }

    const data = document.getElementById('project-status');
    setTimeout(() => {
      data.click();
    }, 200);
  }

  getProjectStatus() {
    this.subscriptionManager.add(
      this.projectService.getProjectStatus().subscribe((res) => {
        const projectStatuses = res.data?.project_statuses || [];
        this.defaultStatuses = projectStatuses.filter((status) => status.project_status.is_default).map((status) => status.project_status.name);

        this.statuses = projectStatuses.map((status) => ({
          label: status.project_status.name,
          value: status.project_status.name
        }));
        this.defaultFilters();
      })
    );
  }

  statusSelected(event) {
    this.dataFilter.project_statuses = event?.value?.toString();
  }

  getClientGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'customers'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const clientGrp = [];
        response?.data?.query_filters?.map((query) => {
          clientGrp.push({
            label: query.query_filter.name,
            value: {
              name: query.query_filter.name,
              value: query.query_filter.query_string
            }
          });
        });
        this.clientGroup = clientGrp;
        this.sortList(this.clientGroup);
        this.cdf.detectChanges();
      })
    );
  }

  getProjectGroup() {
    const requestObject = {
      resource: 'projects'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const projectGrp = [];
        response?.data?.query_filters?.map((query) => {
          projectGrp.push({
            label: query.query_filter.name,
            value: {
              name: query.query_filter.name,
              value: query.query_filter.query_string
            }
          });
        });
        this.projectGroup = projectGrp;
        this.sortList(this.projectGroup);
        this.cdf.detectChanges();
      })
    );
  }

  getProjectList() {
    this.subscriptionManager.add(
      this.utilizationService.getProjectList().subscribe((res) => {
        res.data.projects.forEach((project) => {
          this.projectList.push({
            label: `${project.project?.customer.name} : ${project.project.name}`,
            name: project.project.name,
            value: project.project.id.toString(),
            status: project?.project?.status
          });
        });
        this.sortList(this.projectList);
        this.positionReportData?.forEach((pos) => {
          pos.position.project.status = this.projectList.find((x) => x.value == pos.position.project.id)?.status;
        });
        this.cdf.detectChanges();
      })
    );
  }

  getPositionList() {
    this.subscriptionManager.add(
      this.utilizationService.getPositionList().subscribe((res) => {
        res?.data?.positions?.map((position) => {
          if (position.position.name !== 'project_expenses') {
            this.positionList.push({
              label: position.position.name,
              value: position.position.id
            });
          }
        });
        this.sortList(this.positionList);
        this.dataFilter.position_ids = this.dataFilter.position_ids;
        if (this.dataFilter.position_ids) {
          const position = this.positionList.find((position) => position.value === Number(this.dataFilter.position_ids));
          this.tags.push({
            label: 'Position',
            value: position?.label,
            key: ['position_ids']
          });
        }
        this.applyAllFilter();
        if (this.dataFilter.position_ids) {
          this.dataFilter.position_ids = Number(this.dataFilter.position_ids);
        }
        this.cdf.detectChanges();
      })
    );
  }

  setExportHeaders(): void {
    this.exportPdfColumns = [...this.selectedColumns];

    const addColumn = (dataKeyPrefix, titlePrefix) => {
      this.tableHeaders.forEach((header) => {
        const dataKey = `${dataKeyPrefix} In ${header.monthLabel}`;
        const title = `${titlePrefix} In ${header.monthLabel}`;
        this.exportPdfColumns.push({
          title,
          dataKey,
          bydefaultSelected: false
        });
        this.excelHeaders[0][dataKey] = title;
        this.csvCols.push(dataKey);
      });
    };

    this.exportPdfColumns?.forEach((res) => {
      switch (res.dataKey) {
        case 'revenue':
          addColumn('Revenue', 'Revenue');
          break;
        case 'monthly_cost':
          addColumn('Cost', 'Cost');
          break;
        case 'margins':
          addColumn('Margins', 'Margins');
          break;
        case 'monthly_hours':
          addColumn('Projected Hours', 'Projected Hours');
          break;
        case 'monthly_Variance':
          addColumn('Variance', 'Variance');
          break;
        case 'monthly_ActualHours':
          addColumn('Actual Hours', 'Actual Hours');
          break;
      }
    });

    if (!this.showBillRate) {
      this.exportPdfColumns = this.exportPdfColumns?.filter((col) => col.title !== 'Bill Rate');
    }

    this.csvCols = this.exportPdfColumns?.map((col) => col.dataKey);
    this.excelHeaders = [
      this.exportPdfColumns?.reduce((acc, item) => {
        acc[item.dataKey] = item.title;
        return acc;
      }, {})
    ];

    if (!this.showBillRate) {
      delete this.excelHeaders[0]?.bill_rate;
    }
  }

  arrangeColumns(): void {
    const orderedColumns = [];
    const frozenColumnKeys = new Set(this.frozenCols.map((col) => col.dataKey));

    for (let frozenCol of this.frozenCols) {
      const column = this.selectedColumns.find((col) => col.dataKey === frozenCol.dataKey);
      if (column) {
        orderedColumns.push(column);
      }
    }

    const unOrderedColumns = this.selectedColumns.filter((col) => !frozenColumnKeys.has(col.dataKey));
    this.selectedColumns = [...orderedColumns, ...unOrderedColumns];
  }
  @Debounce(200)
  async loadReport(event?: LazyLoadEvent, dt?) {
    const params = {
      limit: this.dataFilter.limit || this.pageSize,
      offset: this.dataFilter.offset || this.page
    };
    this.showApplyMsg = false;
    this.sidebarParams.template.close();
    this.openFilter = false;
    this.positionReportData = [];
    let queryFilter: QueryFilterParams = {
      // todo further use
      // order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort(event)) : this.activeSort(event),
      // open_positions: true,
      projection_detail_level: 'position_monthly',
      visibility: 'Public'
    };

    if (this.selectedSkillSetObj) {
      this.dataFilter.position_type_id = this.selectedSkillSetObj.id;
    }

    this.dateRangeCalculation();

    if (this.selectedTags.length === 0) {
      this.dataFilter.tags = '';
    }
    if (this.dataFilter.tags) {
      this.treeViewSelectedTags = this.getExtractedTagsFromSelectedTags(this.dataFilter.tags.split(','));
    }
    // todo further use
    // if (!this.sortColumnFlag) {
    //   this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : event?.sortField;
    //   this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by.split(':')[0] === 'asc' ? 1 : -1) : event?.sortOrder;
    // }
    if (this.groupByClient) {
      this.groupByClientCalenderView = this.groupByClient;
    } else {
      this.groupByClientCalenderView = false;
    }

    if (this.groupByEmployee) {
      this.groupByEmployeeCalenderView = this.groupByEmployee;
    } else {
      this.groupByEmployeeCalenderView = false;
    }

    if (this.groupByProject) {
      this.groupByProjectCalenderView = this.groupByProject;
    } else {
      this.groupByProjectCalenderView = false;
    }

    if (this.dataFilter.filterView === 'table') {
      this.switchToCalendarView();
    } else if (this.dataFilter.filterView === 'calendar') {
      this.switchToTableView();
    }
    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter, ...queryFilter, ...params }, 'positions-report');
      for (const [key] of Object.entries(this.dataFilter)) {
        if (key === 'employee_type_name') {
          queryFilter[`${key}`] = this.dataFilter[key].employee_type.name;
        } else {
          if (key !== 'order_by') queryFilter[`${key}`] = this.dataFilter[key];
        }
      }
    }
    // fixing this to 500 for now as the default limit is being set to 50 from the backend, if data goes beyond 50, user will not be able to see them so for now keeping the higher limit as 500
    // 11/3/22: API is now taking care of pagination so if we need to implement pagination we will start making use of these 2 param limit and offset, however if nothing is passed for limit backend will return all the available data in the database. done in OG-746
    // queryFilter['limit'] = 500;
    // queryFilter['offset'] = 0;
    queryFilter = { ...queryFilter, ...params, order_by: this.activeSort(event) };
    delete queryFilter['date'];
    delete queryFilter['customer_name'];
    delete queryFilter['start_month'];
    delete queryFilter['end_month'];
    delete queryFilter['project_name'];
    // delete queryFilter['customer_ids'];
    delete queryFilter['client'];
    delete queryFilter['value'];
    delete queryFilter['year'];
    delete queryFilter['ClientName'];
    delete queryFilter['employeeName'];
    delete queryFilter['variance'];
    delete queryFilter['name'];
    delete queryFilter['projectName'];
    if (queryFilter['order_by'] === null) {
      delete queryFilter['order_by'];
    }
    const duplicateQueryFilter = JSON.parse(JSON.stringify(queryFilter));
    queryFilter = this.queryStringUtil(queryFilter);
    this.applyTags();
    this.loading = true;
    this.loadingReport = true;
    this.preparingCalendarData = true;
    this.subscriptionManager.add(
      this.utilizationService.getOpenPositionReport(queryFilter).subscribe(
        async (res) => {
          this.totalRecords = Number(res.headers.get('x-total-count'));
          this.showPaginator = this.totalRecords <= 10 ? false : true; // Total records from the backend
          this.positionReportData = res.body.data?.positions ? res.body.data?.positions : [];
          this.positionReportData?.forEach((pos) => {
            pos.position.project.status = this.projectList.find((x) => x.value == pos.position.project.id)?.status;
          });
          this.positionReportData.forEach((pos) => {
            pos.position.employee.employeeFullName = pos.position.employee.first_name ? pos.position.employee.first_name + ' ' + pos.position.employee.last_name : '--Open--';
            pos.position.weekly_billable_hours = (+pos?.position?.daily_billable_hours * 5)?.toString();
          });
          this.makeRowsSameHeight();
          this.applyAllFilter(dt);
          await this.prepareCalendarWithProjectsAndAllocations();
          this.preparingCalendarData = false;
          this.loading = false;
          this.loadingReport = false;
          this.closeExtendFiledPopup();
          this.cdf.detectChanges();
        },
        () => {
          this.loading = false;
          this.loadingReport = false;
        }
      )
    );

    delete duplicateQueryFilter['limit'];
    delete duplicateQueryFilter['offset'];

    if (JSON.stringify(this.lastQueryFilter) != JSON.stringify(duplicateQueryFilter)) {
      this.lastQueryFilter = duplicateQueryFilter;
      this.downloadReportInBackground(duplicateQueryFilter);
    }
  }

  get finalizedTags(): string {
    this.finalTagsAlongWithTheCategory = [];
    const categoryAndTags = this.selectedTags;
    this.selectedCategoriesTag = categoryAndTags.filter((tag) => !tag.hasOwnProperty('selectable'));
    for (const selectedTags of this.selectedCategoriesTag) {
      let labelHolder = '';
      labelHolder += selectedTags.label;
      if (selectedTags.parent) {
        labelHolder = selectedTags.parent.label + '__' + labelHolder;
        if (selectedTags.parent?.parent) {
          labelHolder = 'equals:' + selectedTags.parent?.parent?.label + '__' + labelHolder;
          if (selectedTags.parent?.parent?.parent) {
            labelHolder = selectedTags.parent?.parent?.parent?.label + '__' + labelHolder;
          }
        }
      }
      this.finalTagsAlongWithTheCategory.push(labelHolder);
    }
    return this.finalTagsAlongWithTheCategory.toString();
  }

  makeRowsSameHeight() {
    setTimeout(() => {
      if (document.getElementsByClassName('p-datatable-scrollable-wrapper').length) {
        const wrapper = document.getElementsByClassName('p-datatable-scrollable-wrapper');
        for (let i = 0; i < wrapper.length; i++) {
          const w = wrapper.item(i) as HTMLElement;
          const frozen_rows: any = w.querySelectorAll('.p-datatable-frozen-view tr');
          const unfrozen_rows: any = w.querySelectorAll('.p-datatable-unfrozen-view tr');
          for (let i = 0; i < frozen_rows.length; i++) {
            if (frozen_rows[i].clientHeight > unfrozen_rows[i].clientHeight) {
              unfrozen_rows[i].style.height = frozen_rows[i].clientHeight + 'px';
            } else if (frozen_rows[i].clientHeight < unfrozen_rows[i].clientHeight) {
              frozen_rows[i].style.height = unfrozen_rows[i].clientHeight + 'px';
            }
          }
        }
      }
      this.layoutConfigService.updateHeight$.next(true);
      this.height = height;
    });
  }

  prepareHeaders() {
    this.dateRangeCalculation();
    this.tableHeaders = [];
    this.exportReportData = [];
    this.excelExportReportData = [];
    this.tableHeaders = this.getAllDates(new Date(this.dataFilter.end_date_gte), new Date(this.dataFilter.start_date_lt));
    this.arrangeColumns();
    this.setExportHeaders();
    this.removeDuplicateTableHeaders();
    if (this.positionExportData?.length) {
      for (const position of this.positionExportData) {
        const exportData = this.createExportData(position);

        this.exportPdfColumns?.forEach((res) => {
          if (['revenue', 'monthly_cost', 'margins', 'monthly_hours', 'monthly_Variance', 'monthly_ActualHours'].includes(res.dataKey)) {
            this.calculateMonthlyData(exportData, position, res);
          } else if (['billable_hours', 'actual_hours'].includes(res.dataKey)) {
            exportData[res.dataKey] = position?.position?.total[res.dataKey]?.toString() ?? '--';
          } else if (res.dataKey === 'variance') {
            const { billable_hours, actual_hours } = position?.position?.total;
            exportData[res.dataKey] = `${+billable_hours - +actual_hours}`;
          }
        });

        if (exportData && !exportData.hasOwnProperty('bill_rate')) {
          delete exportData['bill_rate'];
        }
        const excelExportData = { ...exportData };

        this.exportReportData.push(exportData);
        this.excelExportReportData.push(excelExportData);
      }
      this.cleanupPdfExportHeader();
      this.cleanupExcelHeaders();
      this.filterExcelDataWithSelectedColums();
    }

    this.layoutConfigService.updateHeight$.next(true);
    this.height = height;
    this.exportButtonDisable = false;
    this.downloadButtonDisable = false;
    this.cdf.detectChanges();
  }

  createExportData(position) {
    const format = 'MM/dd/yyyy';
    const employeeFirstName = position?.position?.employee?.first_name || '';
    const employeeLastName = position?.position?.employee?.last_name || '';

    const existingObj = {
      customer_name: position?.position?.project?.customer?.name,
      project_name: position?.position?.project?.name,
      position_name: position?.position?.name,
      project_status: position?.position?.project?.status,
      employee_full_name: `${employeeFirstName} ${employeeLastName}`,
      employee_type: position?.position?.type,
      bill_rate: this.showBillRate ? position?.position?.bill_rate : undefined,
      daily_billable_hours: position?.position?.daily_billable_hours,
      weekly_billable_hours: (position?.position?.daily_billable_hours * 5).toFixed(2),
      cost: position?.position?.cost,
      tags: this.getExtractedTagsFromSelectedTags(position.position.tags).toString(),
      start_date: this.datePipe.transform(position?.position?.start_date, format),
      end_date: this.datePipe.transform(position?.position?.end_date, format)
    };

    const mergedObject = (this.processObj(position) || []).reduce((acc, obj) => ({ ...acc, ...obj }), existingObj);
    return mergedObject;
  }

  calculateMonthlyData(exportData, position: OpenPositionReport, res: PdfColumnHeaderObj) {
    this.tableHeaders.forEach((header) => {
      const index = position?.position?.validated_monthly_positions?.findIndex(
        (res) => res?.validated_monthly_position?.year === +header.year && res?.validated_monthly_position?.month === +header.month
      );

      const key = `${res.title} In ${header.monthLabel}`;

      if (index === -1) {
        exportData[key] = '--';
      } else {
        exportData[key] = this.getMonthlyDataValue(position, res, index);
      }
    });
  }

  getMonthlyDataValue(position: OpenPositionReport, res: PdfColumnHeaderObj, index: number) {
    switch (res.dataKey) {
      case 'revenue':
        return `$${this.commaNumberPipe.transform(position?.position?.validated_monthly_positions[index]?.validated_monthly_position?.revenue)}`;
      case 'monthly_cost':
        return `$${this.commaNumberPipe.transform(position?.position?.validated_monthly_positions[index]?.validated_monthly_position?.cost)}`;
      case 'margins':
        const margin = (+position?.position?.validated_monthly_positions[index]?.validated_monthly_position?.percent_gross_margin * 100).toFixed(2);
        return `${margin}%`;
      case 'monthly_hours':
        return position?.position?.validated_monthly_positions[index]?.validated_monthly_position?.billable_hours;
      case 'monthly_ActualHours':
        return position?.position?.validated_monthly_positions[index]?.validated_monthly_position?.actual_hours;
      case 'monthly_Variance':
        const { billable_hours, actual_hours } = position?.position?.validated_monthly_positions[index]?.validated_monthly_position;
        const variance = +billable_hours - +actual_hours;
        return variance ? variance.toString() : '0.0';
      default:
        return '';
    }
  }

  cleanupPdfExportHeader(): void {
    this.exportPdfColumns = this.exportPdfColumns.filter(
      (res) => !['revenue', 'margins', 'monthly_hours', 'monthly_ActualHours', 'monthly_Variance', 'monthly_cost'].includes(res.dataKey)
    );
  }

  cleanupExcelHeaders(): void {
    for (const key in this.excelHeaders[0]) {
      if (['revenue', 'margins', 'monthly_hours', 'monthly_ActualHours', 'monthly_Variance', 'monthly_cost'].includes(key)) {
        delete this.excelHeaders[0][key];
      }
    }
  }

  filterExcelDataWithSelectedColums() {
    this.excelExportReportData = this.excelExportReportData.map((res) => {
      const filteredData = {};
      for (const key in this.excelHeaders[0]) {
        if (res[key]) {
          filteredData[key] = res[key];
        }
      }
      return filteredData;
    });
  }

  removeDuplicateTableHeaders() {
    const seen = new Set();
    this.tableHeaders = this.tableHeaders.filter((el) => {
      const duplicate = seen.has(el.id);
      seen.add(el.id);
      return !duplicate;
    });
  }

  getAllDates(startDate: Date, endDate: Date): TableHeader[] {
    const monthHeaderArray: TableHeader[] = [];

    const startUTCDate = new Date(startDate.toISOString());
    const endUTCDate = new Date(endDate.toISOString());

    const cDate = new Date(startUTCDate);
    while (cDate <= endUTCDate) {
      const tableHeader: TableHeader = {
        field: `${MONTH_NAMES[cDate.getUTCMonth()]} ${cDate.getUTCFullYear()}`,
        month: cDate.getUTCMonth() + 1,
        monthLabel: `${MONTH_NAMES[cDate.getUTCMonth()]} ${cDate.getUTCFullYear()}`,
        year: cDate.getUTCFullYear().toString(),
        id: Number(`${cDate.getUTCFullYear()}${cDate.getUTCMonth()}`)
      };

      monthHeaderArray.push(tableHeader);
      cDate.setUTCMonth(cDate.getUTCMonth() + 1);
    }

    return monthHeaderArray;
  }

  dateRangeCalculation() {
    if (this.dataFilter.rollingOption) {
      const { startDate, endDate } = this.getStartDateEndDateFromRolling(this.dataFilter.rollingOption);
      this.dataFilter.end_date_gte = this.datePipe.transform(startDate.toString(), AppConstants.format);
      this.dataFilter.start_date_lt = this.datePipe.transform(endDate.toString(), AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.quarter = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (this.dataFilter.year) {
      const startEndDate = this.getStartEndDateFromYear(this.dataFilter.year);
      this.dataFilter.end_date_gte = this.datePipe.transform(startEndDate.start_date.toString(), AppConstants.format);
      this.dataFilter.start_date_lt = this.datePipe.transform(startEndDate.end_date.toString(), AppConstants.format);
      this.dataFilter.quarter = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
    if (this.dataFilter.quarter) {
      const startEndDate = this.getStartEndDateFromQuarter(this.dataFilter.quarter);
      this.dataFilter.end_date_gte = this.datePipe.transform(startEndDate.start_date, AppConstants.format);
      this.dataFilter.start_date_lt = this.datePipe.transform(startEndDate.end_date, AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (!this.dataFilter.start_date_lt && !this.dataFilter.end_date_gte) {
      const date = new Date();
      this.dataFilter.end_date_gte = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth(), 1), AppConstants.format);
      this.dataFilter.start_date_lt = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth() + 6, 0), AppConstants.format);
    }
  }

  getValues(header, data) {
    for (const position of data?.position?.validated_monthly_positions) {
      const keys = Object.keys(position.validated_monthly_position);
      if (keys.includes(header.monthLabel)) {
        return Math.round(position.validated_monthly_position.revenue);
      }
    }
    return 0;
  }

  activeSort(event: LazyLoadEvent) {
    if (event?.sortField) {
      // identifying if there are multiple sort field in single event handler, for an example firstName and lastName for employee field.
      if (event?.sortField?.includes(':')) {
        const sortFields = event.sortField?.split(':');
        if (event.sortOrder === 1) {
          return `asc:${sortFields[0]}_:asc:${sortFields[1]}`;
        } else {
          return `desc:${sortFields[0]}_:desc:${sortFields[1]}`;
        }
      } else {
        if (event.sortOrder === 1) {
          return 'asc:' + event.sortField;
        } else {
          return 'desc:' + event.sortField;
        }
      }
    }
    return null;
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams, isSaveFilter = false) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
        if ((key === 'rollingOption' && queryStringParam['start_date_lt'] && queryStringParam['end_date_gte'], isSaveFilter)) {
          delete queryStringParam['start_date_lt'];
          delete queryStringParam['end_date_gte'];
        }
      }
    }
    let params = new HttpParams();
    for (const property in queryStringParam) {
      if (queryStringParam[property]) {
        if (property === 'order_by' && queryStringParam[property].includes('_:')) {
          const orderByMultipleProperties = queryStringParam[property].split('_:');
          params = params.append(property, orderByMultipleProperties[0]);
          params = params.append(property, orderByMultipleProperties[1]);
        } else {
          params = params.append(property, queryStringParam[property]);
        }
      } else {
        // check for open_positions property, if it is false then we may consider it to be send as false
        if (property === 'open_positions') {
          params = params.append('open_positions', 'false');
        }
      }
    }
    const paramsArray: any = params;
    for (const key in paramsArray) {
      if (key === 'updates' || key === 'cloneFrom' || key === 'encoder') {
        delete paramsArray.key;
      }
    }
    return paramsArray;
  }

  switchToTableView() {
    this.updatingButton = true;
    for (const button of this.buttons) {
      if (button.isSwitcherButton) {
        if (button.viewType === 'calendar') {
          button.isActive = true;
          button.btnSvg = 'calendar-active';
          this.tableButtonActive = false;
        } else {
          button.isActive = false;
          button.btnSvg = 'table';
        }
      }
    }
    this.calendarButtonActive = true;
    this.tableButtonActive = false;
    this.updatingButton = false;
    this.cdf.detectChanges();
  }

  switchToCalendarView() {
    this.updatingButton = true;
    for (const button of this.buttons) {
      if (button.isSwitcherButton) {
        if (button.viewType === 'table') {
          button.isActive = true;
          button.btnSvg = 'table-active';
        } else {
          button.isActive = false;
          button.btnSvg = 'calendar';
        }
      }
    }
    this.calendarButtonActive = false;
    this.tableButtonActive = true;
    this.updatingButton = false;
    this.cdf.detectChanges();
  }

  openSideBar() {
    this.sidebarParams = { template: this.el };
    this.sidebarParams?.template?.open();
    this.openFilter = true;
  }

  onClose(remember: boolean = false) {
    this.sidebarParams.template.close();
    this.openFilter = false;
    this.dateRequired = false;
    this.dateError = false;
    if (remember) {
      this.dataFilter = this.cacheFilter.getCacheFilters('positions-report');
    }
  }

  clearFilters() {
    this.dataFilter = new IFilter();
    this.projectStatus = this.defaultStatuses;
    this.dateRequired = false;
    this.dateError = false;
    this.ClientName = null;
    this.selectedClientName = [];
    this.projectName = null;
    this.selectedProjectName = [];
    this.employeeName = null;
    this.selectedEmployeeName = [];
    this.showEmployeeFilter = false;
    this.showClientFilter = false;
    this.showProjectFilter = false;
    this.defaultView = 'table';
    this.dataFilter.filterView = 'table';
    this.defaultFilters();
    const date = new Date();
    this.dataFilter.rollingOption = 'Current plus 2 months';
    this.selectedColumns = this.frozenCols;
    this._pCols = this.selectedColumns?.map((f) => f.dataKey);
    this.columnToggle.setSelectedColumns(this.selectedColumns, 'openPositionReport');
    this.finalTagsAlongWithTheCategory = [];
    this.selectedTags = [];
    this.groupByClient = false;
  }

  filterReport() {
    if (this.dataFilter.start_month || this.dataFilter.end_month) {
      if (!this.dataFilter.start_month) {
        this.dateRequired = true;
      }
      if (!this.dataFilter.end_month) {
        this.dateRequired = true;
      }
    }

    if (this.dataFilter?.value?.value) {
      this.dataFilter.employee_ids = this.dataFilter.value.value;
    }
    if (!this.dateError && !this.dateRequired) {
      this.applyTags();
      this.resetTableSortAndPagination();
      this.loadReport();
      this.onClose();
    }
  }

  exportReport(type) {
    if (type === 'csv') {
      this.utilizationService.exportToCsv(this.exportReportData, 'staffedPositionReport', this.csvCols);
    }
    if (type === 'pdf') {
      // const staticColumsCount = this.exportPdfColumns.filter(
      //   (f) => f.bydefaultSelected !== false
      // ).length;
      // if (
      //   this.exportPdfColumns.length > 15 &&
      //   this.exportPdfColumns.length - staticColumsCount > 4
      // ) {
      //   this.unableToDownloadPdf = true;
      // } else {
      this.utilizationService.exportPdfManageTimesheet(this.exportPdfColumns, this.exportReportData, 'staffedPositionReport', 6);
      // }
    }
    if (type === 'excel') {
      this.utilizationService.exportExcel(this.excelHeaders, this.excelExportReportData, 'staffedPositionReport');
    }
  }

  dateSelected() {
    if (this.dataFilter.date[1] !== null) {
      this.dataFilter.start_date_lt = this.datePipe.transform(this.dataFilter.date[1], AppConstants.format);
    }
    if (this.dataFilter.date[0] !== null) {
      this.dataFilter.end_date_gte = this.datePipe.transform(this.dataFilter.date[0], AppConstants.format);
    }
  }

  startMonthSelected(event) {
    this.dateError = false;
    this.dateRequired = false;
    this.dataFilter.end_date_gte = this.datePipe.transform(event, AppConstants.format);
    this.dataFilter.start_month = moment(this.dataFilter.end_date_gte).toDate();
    if (this.dataFilter.start_date_lt && this.dataFilter.end_date_gte) {
      if (new Date(this.dataFilter.start_date_lt) < new Date(this.dataFilter.end_date_gte)) {
        this.dateError = true;
      }
    }
  }
  endMonthSelected(event) {
    this.dateError = false;
    this.dateRequired = false;
    let date = new Date(event);
    date = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    this.dataFilter.start_date_lt = this.datePipe.transform(date, AppConstants.format);
    this.dataFilter.end_month = moment(this.dataFilter.start_date_lt).toDate();
    if (this.dataFilter.start_date_lt && this.dataFilter.end_date_gte) {
      if (new Date(this.dataFilter.start_date_lt) < new Date(this.dataFilter.end_date_gte)) {
        this.dateError = true;
      }
    }
  }
  getEmployeeIds() {
    const paramsToRemove = ['offset', 'limit'];
    const param = this.removeParams(this.dataFilter.name.value.split('&'), paramsToRemove);
    this.subscriptionManager.add(
      this.utilizationService.getEmployeeIds(param).subscribe((res) => {
        if (res?.employee_ids) {
          this.dataFilter.employee_ids = res.employee_ids.join(',');
        } else {
          this.dataFilter.employee_ids = '';
        }
        this.cdf.detectChanges();
      })
    );
    this.dataFilter.emp_grp_name = this.dataFilter.name.name;
    this.dataFilter.emp_grp_value = this.dataFilter.name.value;
  }

  getClientIds() {
    const paramsToRemove = ['offset', 'limit'];
    const param = this.removeParams(this.dataFilter.customer_name.value.split('&'), paramsToRemove);
    this.subscriptionManager.add(
      this.utilizationService.getClientIds(param).subscribe((res) => {
        if (res?.customer_ids) {
          this.dataFilter.customer_ids = res.customer_ids.join(',');
        } else {
          this.dataFilter.customer_ids = '';
        }
        this.cdf.detectChanges();
      })
    );
    this.dataFilter.client_grp_name = this.dataFilter.customer_name.name;
    this.dataFilter.client_grp_value = this.dataFilter.customer_name.value;
  }

  getProjectIds() {
    const paramsToRemove = ['offset', 'limit'];
    const param = this.removeParams(this.dataFilter.project_name.value.split('&'), paramsToRemove);
    this.subscriptionManager.add(
      this.utilizationService.getProjectIds(param).subscribe((res) => {
        if (res?.project_ids) {
          this.dataFilter.project_ids = res.project_ids.join(',');
        } else {
          this.dataFilter.project_ids = '';
        }
        this.cdf.detectChanges();
      })
    );
    this.dataFilter.project_grp_name = this.dataFilter.project_name.name;
    this.dataFilter.project_grp_value = this.dataFilter.project_name.value;
  }

  removeParams(params, paramsToRemove) {
    return params
      .filter((param) => {
        const [key, value] = param.split('=');
        return !paramsToRemove.includes(key) && value !== '' && value !== null;
      })
      .join('&');
  }

  applyTags() {
    this.tags = [];
    if (!this.dataFilter) {
      return;
    }

    if (this.dataFilter.start_date_lt && this.dataFilter.end_date_gte && !this.dataFilter.year && !this.dataFilter.quarter && !this.dataFilter.rollingOption) {
      const dateFormat = 'MM/dd/yyyy';
      const value = this.datePipe.transform(this.dataFilter.end_date_gte, dateFormat) + ' - ' + this.datePipe.transform(this.dataFilter.start_date_lt, dateFormat);
      this.tags.push({
        label: 'Date Range',
        value: value,
        key: ['start_date_lt', 'end_date_gte', 'start_month', 'end_month']
      });
    }

    if (this.dataFilter.rollingOption) {
      this.tags.push({
        label: 'Rolling',
        value: this.dataFilter.rollingOption.toString(),
        key: ['start_date_lt', 'end_date_gte', 'rollingOption']
      });
    }

    if (this.dataFilter.year) {
      this.tags.push({
        label: 'By Year',
        value: this.dataFilter.year.toString(),
        key: ['start_date_lt', 'end_date_gte', 'year']
      });
    }

    if (this.dataFilter.quarter) {
      this.tags.push({
        label: 'By Quater',
        value: this.dataFilter.quarter.toString(),
        key: ['start_date_lt', 'end_date_gte', 'quarter']
      });
    }

    if (this.dataFilter.project_name) {
      this.tags.push({
        label: 'Project Group',
        value: this.dataFilter.project_name.name,
        key: ['project_name', 'project_ids']
      });
    }

    if (this.dataFilter.customer_name) {
      this.tags.push({
        label: 'Client Group',
        value: this.dataFilter.customer_name.name,
        key: ['customer_name', 'customer_ids']
      });
    }
    // if(this.dataFilter.client) {
    //   this.tags.push({
    //     label: "Client",
    //     value: this.dataFilter.client.name,
    //     key: ['client','clientProjectIds']
    //   });
    // }

    if (this.dataFilter.position_ids) {
      const position = this.positionList.find((position) => position.value === Number(this.dataFilter.position_ids));
      if (position) {
        this.tags.push({
          label: 'Position',
          value: position?.label,
          key: ['position_ids']
        });
      }
    }
    if (this.dataFilter.project_statuses) {
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.project_statuses,
        key: ['project_statuses']
      });
    }

    if (this.dataFilter?.ClientName?.length) {
      this.tags.push({
        label: 'Client',
        value: this.selectedClientName.join(','),
        key: ['ClientName', 'customer_ids']
      });
    }
    if (this.dataFilter?.projectName?.length) {
      this.tags.push({
        label: 'Project',
        value: this.selectedProjectName.join(','),
        key: ['projectName', 'project_ids']
      });
    }

    if (this.dataFilter.name) {
      this.tags.push({
        label: 'Employee Group',
        value: this.dataFilter.name.name,
        key: ['name', 'employee_ids']
      });
    }

    if (this.dataFilter.value) {
      this.tags.push({
        label: 'Employee',
        value: this.dataFilter.value.name,
        key: ['value', 'employee_ids']
      });
    }
    if (this.dataFilter?.employeeName?.length) {
      this.tags.push({
        label: 'Employee',
        value: this.selectedEmployeeName.join(','),
        key: ['employeeName', 'employee_ids']
      });
    }

    if (this.dataFilter?.open_positions?.toString() === 'true') {
      this.tags.push({
        label: 'Open Positions',
        value: 'Yes',
        key: ['open_positions']
      });
    }

    if (this.dataFilter?.position_type_id) {
      this.tags.push({
        label: 'Skill Set',
        value: this.selectedSkillSetObj?.label,
        key: ['position_type_id']
      });
    }
  }

  onCloseFromFilterPanel(tagValue) {
    tagValue?.key.forEach((key) => {
      if (key === 'clientName' || key === 'ClientName') {
        this.dataFilter[key] = null;
        this.ClientName = null;
        this.selectedClientName = [];
        this.dataFilter.project_ids = null;
      } else if (key === 'projectName') {
        this.dataFilter[key] = null;
        this.projectName = null;
        this.dataFilter.project_ids = null;
        this.selectedProjectName = [];
      } else if (key === 'employeeName') {
        this.dataFilter[key] = null;
        this.employeeName = null;
        this.selectedEmployeeName = [];
      } else if (key === 'position_type_id') {
        this.dataFilter[key] = null;
        this.selectedSkillSetObj = null;
      } else {
        this.dataFilter[key] = null;
      }
    });
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: null },
      queryParamsHandling: 'merge'
    });
    this.doFilterData();
  }

  clientSelected(event) {
    this.dataFilter.customer_ids = event?.value?.toString();
    let clientName = this.client.filter((elist) => elist.value == event.itemValue);
    if (clientName.length && clientName[0]?.label) {
      if (this.selectedClientName.includes(clientName[0]?.label)) {
        this.selectedClientName = this.selectedClientName.filter((emp) => emp !== clientName[0].label);
      } else {
        this.selectedClientName.push(clientName[0]?.label);
      }
    }
    this.dataFilter.ClientName = this.selectedClientName;
  }

  projectSelected(event) {
    this.dataFilter.project_ids = event?.value?.toString();
    let projectName = this.projectList.filter((elist) => elist.value == event.itemValue);
    if (projectName.length && projectName[0]?.name) {
      if (this.selectedProjectName.includes(projectName[0]?.name)) {
        this.selectedProjectName = this.selectedProjectName.filter((emp) => emp !== projectName[0]?.name);
      } else {
        this.selectedProjectName.push(projectName[0]?.name);
      }
    }
    this.dataFilter.projectName = this.selectedProjectName;
  }

  tagSelected(event) {
    this.dataFilter.tags = this.finalizedTags;
    this.treeViewSelectedTags = [];
  }

  employeeSelected(event) {
    this.dataFilter.employee_ids = event?.value?.toString();
    let employeeName = this.employeeList.filter((elist) => elist.value == event.itemValue);
    if (employeeName.length && employeeName[0]?.label) {
      if (this.selectedEmployeeName.includes(employeeName[0]?.label)) {
        this.selectedEmployeeName = this.selectedEmployeeName.filter((emp) => emp !== employeeName[0].label);
      } else {
        this.selectedEmployeeName.push(employeeName[0]?.label);
      }
    }
    this.dataFilter.employeeName = this.selectedEmployeeName;
  }

  onRemoveStatusFilter(statusValue) {
    if (statusValue.length) {
      this.dataFilter.project_statuses = statusValue.join(',');
      this.projectStatus = statusValue;
    } else {
      this.dataFilter.project_statuses = null;
      this.projectStatus = null;
    }
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: null },
      queryParamsHandling: 'merge'
    });
    this.doFilterData();
  }

  doFilterData() {
    this.applyTags();
    this.loadReport();
  }

  showHelpData(data) {
    data.showHelpIconData = !data.showHelpIconData;
    this.loading$.next(true);
    this.utilizationService.getProject(data.position.project?.id).subscribe((res) => {
      this.loading$.next(false);
      data.position.customer_name = res?.data?.project?.customer?.name;
      data.position.contact_person = res?.data?.project?.contacts;
      this.cdf.detectChanges();
    });
  }
  getFixCssClass() {
    return;
  }
  getDynCssClass(col) {
    if (col.length <= 3) {
      return 'dynamic-col-3';
    } else {
      return 'dynamic-col-more';
    }
  }
  resetFilter() {
    this.resetTableSortAndPagination();
    this.dataFilter = new IFilter();
    this.cacheFilter.resetCacheFilters('positions-report');
    this.projectStatus = this.defaultStatuses;
    this.tags = [];
    this.selectedClientName = [];
    this.ClientName = null;
    this.showClientFilter = false;
    this.selectedProjectName = [];
    this.projectName = null;
    this.showProjectFilter = false;
    this.selectedEmployeeName = [];
    this.employeeName = null;
    this.defaultView = 'table';
    this.dataFilter.filterView = 'table';
    this.showEmployeeFilter = false;
    this.groupByClient = false;
    this.selectedColumns = this.frozenCols;
    this._pCols = this.selectedColumns?.map((f) => f.dataKey);
    this.columnToggle.setSelectedColumns(this.selectedColumns, 'openPositionReport');
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: null },
      queryParamsHandling: 'merge'
    });
    // reset the filter selection as well
    this.selectedFilterFormControl = new FormControl('');
    this.finalTagsAlongWithTheCategory = [];
    this.selectedTags = [];
    this.dataFilter.rollingOption = 'Current plus 2 months';
    this.defaultFilters();
    this.loadReport();
  }

  openSaveFilterList() {
    this.showFilterListDialog = true;
    this.showSavedFilter = true;
    this.cdf.detectChanges();
  }

  openExportOptionList() {
    this.showExportOptionDialog = true;
    this.showExportOptions = true;
    this.cdf.detectChanges();
  }
  applyAllFilter(dt?) {
    if (this.dataFilter.end_date_gte && this.dataFilter.start_month) {
      this.dataFilter.start_month = moment(this.dataFilter.end_date_gte).toDate();
    }
    if (this.dataFilter.start_date_lt && this.dataFilter.end_month) {
      this.dataFilter.end_month = moment(this.dataFilter.start_date_lt).toDate();
    }
    if (typeof this.dataFilter.selectedColumns === 'string') {
      this.dataFilter.selectedColumns = this.dataFilter.selectedColumns.replace(/%2C/g, ',').replace(/%3A/g, ':');
      this.selectedColumns = this.frozenCols.filter((f) => this.dataFilter.selectedColumns.includes(f.dataKey));
      this._pCols = this.dataFilter.selectedColumns;
      this.prepareHeaders();
      delete this.dataFilter.selectedColumns;
    }
    if (this.dataFilter?.project_statuses) {
      this.dataFilter.project_statuses = this.dataFilter.project_statuses.replace(/%2C/g, ',');
      this.projectStatus = this.dataFilter.project_statuses.split(',');
    }
    if (this.dataFilter?.project_grp_name && this.dataFilter?.project_grp_value) {
      this.dataFilter.project_name = {
        name: this.dataFilter.project_grp_name,
        value: this.dataFilter.project_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&')
      };
    }
    if (this.dataFilter?.client_grp_name && this.dataFilter?.client_grp_value) {
      this.dataFilter.customer_name = {
        name: this.dataFilter.client_grp_name,
        value: this.dataFilter.client_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&')
      };
    }
    if (this.dataFilter?.emp_grp_name && this.dataFilter?.emp_grp_value) {
      this.dataFilter.name = {
        name: this.dataFilter.emp_grp_name,
        value: this.dataFilter.emp_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&')
      };
      this.showEmployeeFilter = true;
    } else {
      if (this.dataFilter.employeeName && this.dataFilter.employee_ids) {
        this.employeeName = this.dataFilter.employee_ids.split(',');
        this.selectedEmployeeName = this.dataFilter.employeeName.toString().replace(/%2C/g, ',').split(',');
      }
    }
    if (this.dataFilter.employee_ids) {
      this.dataFilter.employee_ids = this.dataFilter.employee_ids.replace(/%2C/g, ',');
    }
    if (this.dataFilter.name) {
      this.showEmployeeFilter = true;
    }
    if (this.dataFilter?.customer_ids) {
      this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(/%2C/g, ',');
      this.showClientFilter = true;
    }
    if (this.dataFilter?.project_ids) {
      this.dataFilter.project_ids = this.dataFilter.project_ids.replace(/%2C/g, ',');
      this.showProjectFilter = true;
    }
    if (this.dataFilter.position_ids) {
      this.dataFilter.position_ids = Number(this.dataFilter.position_ids);
    }
    if (this.dataFilter?.position_type_id) {
      this.dataFilter.position_type_id = Number(this.dataFilter.position_type_id);
      this.selectedSkillSetObj = this.skillSetList.find((f) => f.id === this.dataFilter.position_type_id);
    }

    if (this.dataFilter.open_positions) {
      this.dataFilter.open_positions = this.dataFilter.open_positions.toString() === 'true' ? true : false;
    }

    if (this.dataFilter?.ClientName?.length && this.dataFilter.customer_ids) {
      this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(/%2C/g, ',');
      this.ClientName = this.dataFilter.customer_ids.split(',');
      this.selectedClientName = this.dataFilter.ClientName.toString().replace(/%2C/g, ',').split(',');
      this.showClientFilter = false;
    }
    if (this.dataFilter.projectName?.length && this.dataFilter.project_ids) {
      this.dataFilter.project_ids = this.dataFilter.project_ids.replace(/%2C/g, ',');
      this.projectName = this.dataFilter.project_ids.split(',');
      this.selectedProjectName = this.dataFilter.projectName.toString().replace(/%2C/g, ',').split(',');
      this.showProjectFilter = false;
      this.cdf.detectChanges();
    }
    if (this.dataFilter?.tags) {
      this.dataFilter.tags = this.dataFilter.tags.replace(/%3A/g, ':').replace(/%2C/g, ',');
    }

    if (this.dataFilter?.groupByClients) {
      this.groupByClient = this.dataFilter?.groupByClients;
    }
    if (this.dataFilter.groupByEmployee) {
      this.groupByEmployee = this.dataFilter.groupByEmployee;
    }

    // we here have tags being applied, we now need to marke those tags checked in our Tree view data object
    this.onFilterChangePreapareSelectedTreeNodes();

    this.cdf.detectChanges();
    if (this.editModeEnable) {
      // opening the just duplicated item in edit mode
      this.editPositionDetail(this.duplicatedPositionObject, dt);
    }
    this.applyTags();
  }
  applyFilter() {
    this.showSavedFilter = false;
    this.dataFilter = JSON.parse('{"' + decodeURI(this.selectedFilter.query_filter.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    this.dataFilter.offset = this.page = 0;
    this.pageSize = this.dataFilter.limit = Number(this.dataFilter.limit) || 10;
    this.applyAllFilter();
    this.onFilterChangePreapareSelectedTreeNodes();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters;
    this.loadReport();
    this.closeModal();
  }

  closeModal() {
    this.showFilterListDialog = false;
    this.selectedFilter = null;
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
  }

  closePdfModel() {
    this.unableToDownloadPdf = false;
    this.showExportOptionDialog = false;
    this.showExportOptions = false;
  }

  onSaveFilter() {
    if (this._pCols) {
      this.dataFilter.selectedColumns = this._pCols;
    }
    let filter = JSON.parse(JSON.stringify(this.dataFilter));
    filter = this.queryStringUtil(filter, true);
    if (this.dataFilter?.project_name?.name) {
      filter.project_grp_name = this.dataFilter?.project_name?.name;
      filter.project_grp_value = this.dataFilter?.project_name?.value;
    }
    if (this.dataFilter?.customer_name?.name) {
      filter.client_grp_name = this.dataFilter?.customer_name?.name;
      filter.client_grp_value = this.dataFilter?.customer_name?.value;
    }
    if (this.dataFilter?.name?.name) {
      filter.emp_grp_name = this.dataFilter?.name?.name;
      filter.emp_grp_value = this.dataFilter?.name?.value;
    }
    if (this.dataFilter?.value?.name) {
      filter.employee_name = this.dataFilter?.value?.name;
      filter.employee_id = this.dataFilter?.value?.value;
    }

    const requestObject: SaveFilter = {
      query_string: this.serialize(filter),
      resource: 'staffedPositionReport'
    };
    const dialogTitle = 'Save Filter Group';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject);
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        setTimeout(() => {
          this.sidebarParams.template.close();
          this.openFilter = false;
          this.cdf.detectChanges();
        }, 900);
        this.getStoredFilters();
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }

  showSaveClientFilterSelected() {
    this.showClientFilter = !this.showClientFilter;
    this.dataFilter.customer_ids = null;
    this.dataFilter.customer_name = null;
    this.selectedClientName = [];
    this.dataFilter.ClientName = [];
    this.dataFilter.client_grp_name = null;
    this.dataFilter.client_grp_value = null;
    this.ClientName = null;
    this.cdf.detectChanges();
  }

  showGroupByClient() {
    this.groupByClient = !this.groupByClient;
    this.dataFilter.groupByClients = this.groupByClient;
    this.groupByEmployee = false;
    this.dataFilter.groupByEmployee = false;
    this.groupByProject = false;
    this.dataFilter.groupByProject = false;
  }

  showGroupByEmplpoyee() {
    this.groupByEmployee = !this.groupByEmployee;
    this.dataFilter.groupByEmployee = this.groupByEmployee;
    this.groupByClient = false;
    this.dataFilter.groupByClients = false;
    this.groupByProject = false;
    this.dataFilter.groupByProject = false;
  }

  showGroupByProject() {
    this.groupByProject = !this.groupByProject;
    this.dataFilter.groupByProject = this.groupByProject;
    this.groupByEmployee = false;
    this.dataFilter.groupByEmployee = false;
    this.groupByClient = false;
    this.dataFilter.groupByClients = false;
  }

  setDefaultView(view: string) {
    this.defaultView = view;
    this.dataFilter.filterView = view;
  }

  showSaveProjectFilterSelected() {
    this.showProjectFilter = !this.showProjectFilter;
    this.dataFilter.project_ids = null;
    this.dataFilter.projectName = [];
    this.selectedProjectName = [];
    this.projectName = null;
    this.dataFilter.project_grp_name = null;
    this.dataFilter.project_grp_value = null;
    this.dataFilter.project_name = null;
    this.cdf.detectChanges();
  }
  showSaveEmployeeFilterSelected() {
    this.showEmployeeFilter = !this.showEmployeeFilter;
    this.dataFilter.employee_ids = null;
    this.dataFilter.employeeName = [];
    this.dataFilter.name = null;
    this.dataFilter.emp_grp_name = null;
    this.dataFilter.emp_grp_value = null;
    this.employeeName = null;
    this.cdf.detectChanges();
  }
  // converts the object in to query param string
  serialize = (obj) => {
    const str = [];
    obj = obj.keys().map((x) => ({ [x]: obj.get(x) }));
    const objKeyValue = [];
    const objectHolder = {};
    for (let item of obj) {
      for (let p in item) {
        Object.assign(objectHolder, { [p]: item[p] });
      }
    }
    objKeyValue.push(objectHolder);
    const iteratingObject = objKeyValue[0];
    for (const p in iteratingObject) {
      if (iteratingObject.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(iteratingObject[p]));
      }
    }
    return str.join('&');
  };

  getStoredFilters() {
    const requestObject = {
      resource: 'staffedPositionReport'
    };
    this.subscriptionManager.add(
      this.utilizationService.getStoredFilters(requestObject).subscribe(
        (res: ISavedFilterList) => {
          this.loading = false;
          this.sharedFilters = [];
          this.myFilters = [];
          this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
          this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
          this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
          this.availableFilters = res.data.query_filters;
          this.cdf.detectChanges();
          this.routerListener();
        },
        () => (this.loading = false)
      )
    );
  }

  getStartDateEndDateFromRolling(rollingOption: string) {
    const currentDate = new Date();
    const rollingRelativeMonth = rollingOption.split(' ')[1];
    const rollingMonths = parseInt(rollingOption.split(' ')[2]);

    const startDate = new Date(currentDate);
    startDate.setDate(1);

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + rollingMonths + 1, 0);

    if (rollingRelativeMonth === 'minus') {
      startDate.setMonth(currentDate.getMonth() - 1);
      endDate.setMonth(currentDate.getMonth() + rollingMonths, 0);
      endDate.setFullYear(currentDate.getFullYear());
    }

    return { startDate, endDate };
  }

  getStartEndDateFromQuarter(quarter) {
    const now = new Date();
    const quarterDates = {
      Q1: {
        start_date: new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 2, 31, 11, 59, 59, 999)
      },
      Q2: {
        start_date: new Date(now.getFullYear(), 3, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 5, 30, 11, 59, 59, 999)
      },
      Q3: {
        start_date: new Date(now.getFullYear(), 6, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 8, 30, 11, 59, 59, 999)
      },
      Q4: {
        start_date: new Date(now.getFullYear(), 9, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 11, 31, 11, 59, 59, 999)
      }
    };
    return quarterDates[quarter];
  }

  getStartEndDateFromYear(year) {
    return {
      start_date: new Date(year, 0, 1, 0, 0, 0, 0),
      end_date: new Date(year, 11, 31, 11, 59, 59, 999)
    };
  }

  getSkillSets() {
    this.subscriptionManager.add(
      this.adminService.getPositionTypes().subscribe((res) => {
        this.loading = false;
        if (res?.body?.data?.position_types?.length > 0) {
          res?.body?.data?.position_types?.forEach((e) => {
            this.skillSetList.push({
              label: e.position_type?.name,
              id: e.position_type.id
            });
          });

          const selectedSkillSet = this.skillSetList.find((skill) => skill.id === this.dataFilter?.position_type_id);
          this.selectedSkillSetObj = selectedSkillSet;
          this.dataFilter.position_type_id = selectedSkillSet ? Number(selectedSkillSet.id) : null;
          this.cdf.detectChanges();
        }
      })
    );
  }
  editTags(tags: Array<string>, position_id: number) {
    const dialogRef = this.dialog.open(AppendTagsComponent, {
      data: {
        tags: tags,
        positionId: position_id,
        title: 'Add Tag',
        flag: 'ADD TAGS TO POSITION'
      },
      width: '880px'
    });
  }

  editPositionDetail(position, dt?) {
    this.positionEdit = true;
    this.clonePosition[position.position.id] = JSON.parse(JSON.stringify(position));
    this.editPositionObj = { ...position };
    this.editPositionObj.position.weekly_billable_hours = +(this.editPositionObj?.position?.daily_billable_hours * 5)?.toFixed(2);
    this.editPositionObj.position.total['variance'] =
      (+this.editPositionObj?.position?.total?.billable_hours - +this.editPositionObj?.position?.total?.actual_hours).toFixed(2) || '0';
    this.editPositionObj.position.start_date = moment.utc(new Date(this.editPositionObj.position.start_date)).format('l LT');
    this.editPositionObj.position.end_date = moment.utc(new Date(this.editPositionObj.position.end_date)).format('l LT');
    const employeedId = position?.position?.employee ? Number(position?.position?.employee?.id) : null;
    this.editPositionObj.position.employee.id = this.employeeList.filter((emp) => emp.id === employeedId)[0];
    this.editPositionObj.position.position_type_id = this.skillSetList.filter((skill) => skill.label === position.position.type)[0];
    this.editPositionObj.position.bill_rate = position?.position.bill_rate === 'n/a' ? 0 : position?.position.bill_rate;
    this.positionMinDate = new Date(this.editPositionObj.position.start_date);
    if (this.editPositionObj.position.employee.id.id === 0) {
      this.editPositionObj.position.employee.id = null;
    }
    dt?.initRowEdit(this.editPositionObj);
    this.editModeEnable = false;
  }

  positionValueChange() {
    this.showPositionError = !this.editPositionObj.position.name;
  }

  isErrorInDailyBillableHours(): boolean {
    return (
      (!this.editPositionObj?.position?.daily_billable_hours && this.editPositionObj?.position?.daily_billable_hours !== 0) ||
      this.editPositionObj?.position?.daily_billable_hours < 0 ||
      this.editPositionObj?.position?.daily_billable_hours > 8
    );
  }

  saveEditPosition(extendFiled: boolean = false) {
    if (this.editPositionObj?.position?.name && this.editPositionObj?.position?.start_date && this.editPositionObj?.position?.end_date && !this.isErrorInDailyBillableHours()) {
      const positionReportData = this.positionReportData;
      this.loading = true;
      this.positionReportData = [];
      let saveEditPosition;

      if (!extendFiled) {
        saveEditPosition = {
          bill_rate: this.editPositionObj.position.bill_rate === 'n/a' ? 0 : this.editPositionObj.position.bill_rate,
          cost: this.editPositionObj?.position?.cost,
          daily_billable_hours: this.editPositionObj?.position?.daily_billable_hours,
          employee_id: this.editPositionObj?.position?.employee?.id?.id ?? this.editPositionObj?.position?.employee?.id ?? null,
          end_date: this.datePipe.transform(new Date(this.editPositionObj.position.end_date), AppConstants.format),
          name: this.editPositionObj.position.name,
          position_type_id: this.editPositionObj?.position?.position_type_id?.id,
          project_id: this.editPositionObj?.position?.project?.id,
          start_date: this.datePipe.transform(new Date(this.editPositionObj?.position?.start_date), AppConstants.format),
          id: this.editPositionObj.position.id,
          extended_fields: this.positionObj?.position?.extended_fields ?? this.editPositionObj?.position?.extended_fields
        };
      } else {
        saveEditPosition = {
          id: this.editPositionObj.position.id,
          extended_fields: this.positionObj?.position?.extended_fields ?? this.editPositionObj?.position?.extended_fields
        };
      }

      if (!saveEditPosition?.position_type_id && !extendFiled) {
        saveEditPosition.position_type_id = this.skillSetList.find((skill) => skill.label === this.editPositionObj?.position?.type)?.id;
      }

      this.subscriptionManager.add(
        this.utilizationService.updatePosition(saveEditPosition).subscribe(
          () => {
            this.loading = false;
            this.positionEdit = false;
            this.closeExtendFiledPopup();
            this.loadReport();
            this.positionMinDate = null;
            this.downloadReportInBackground(this.lastQueryFilter);
            this.cdf.detectChanges();
            this.layoutUtilsService.showActionNotification(this.appConstants.updatePosition, AlertType.Success);
          },
          () => {
            this.loading = false;
            this.positionReportData = positionReportData;
          }
        )
      );
    } else {
      if (!this.editPositionObj?.position?.name) {
        this.showPositionError = true;
      }
      if (!this.editPositionObj?.position?.start_date) {
        this.showStartDateError = true;
      }
      if (!this.editPositionObj?.position?.end_date) {
        this.showEndDateError = true;
      }
    }
  }

  updateEmployeeDetail(event, flag?: boolean) {
    this.subscriptionManager.add(
      this.projectService.getEmployeeDetail(event.value.id).subscribe((res) => {
        if (flag) {
          if (this.editPositionObj.position.employee.id.id === 0) {
            this.editPositionObj.position.employee.id = null;
          } else {
            this.editPositionObj.position.cost = res.data.employee.hourly_cost;
          }
          this.cdf.detectChanges();
        }
      })
    );
  }

  cancelEditPosition(position, index) {
    this.positionReportData[index] = this.clonePosition[position.position.id];
    this.showPositionError = false;
    this.showStartDateError = false;
    this.showEndDateError = false;
    this.positionEdit = false;
    delete this.clonePosition[position.position.id];
  }

  editPositionStartDateSelected() {
    this.showEndDateError = false;
    this.positionMinDate = new Date(this.editPositionObj.position.start_date);
    if (this.positionMinDate > new Date(this.editPositionObj.position.end_date)) {
      this.editPositionObj.position.end_date = null;
      this.showEndDateError = true;
    }
    if (!this.editPositionObj.position.start_date) {
      this.showStartDateError = true;
    } else {
      this.showStartDateError = false;
    }
  }

  //get No of Weeks
  getNoOfWeeks(position) {
    if (position.start_date && position.end_date) {
      const startDate = new Date(position.start_date);
      const endDate = new Date(position.end_date);
      const difference_In_Time = endDate.getTime() - startDate.getTime();
      const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
      return Math.ceil(difference_In_Days / 7) + ' Weeks';
    }
  }
  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter.length
      ? this.availableFilters.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === false);
  }

  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }
  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Share',
      text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Unshare',
      text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        () => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.cdf.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Positions Report');
        },
        () => (this.isSubmitting = false)
      )
    );
  }
  inputFilterName() {
    this.showNameError = false;
  }

  deleteFilter(filterOption) {
    this.showDeleteDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        () => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.cdf.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Positions Report');
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.utilizationService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          () => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.showFilterListDialog = true;
            this.cdf.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      }
    });
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            const filterValue = this.sharedFilters?.find((f) => JSON.stringify(f) === JSON.stringify(this.selectedFilter));
            if (filterValue) {
              this.selectedFilterFormControl.setValue(filterValue);
            }
            this.applyFilter();
          }
        },
        () => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  applySelectedFilterAndUpdateUrl() {
    this.showFilterListDialog = false;
    this.showSavedFilter = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.onClose();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: this.selectedFilter?.query_filter?.id },
      queryParamsHandling: 'merge'
    });
  }

  duplicatePosition(position, dt?) {
    const duplicatePositionObj = { ...position };
    duplicatePositionObj.position.start_date = moment.utc(new Date(duplicatePositionObj.position.start_date)).format('l LT');
    duplicatePositionObj.position.end_date = moment.utc(new Date(duplicatePositionObj.position.end_date)).format('l LT');
    const employeedId = position?.position?.employee ? Number(position?.position?.employee?.id) : null;
    duplicatePositionObj.position.employee.id = this.employeeList.filter((emp) => emp.id === employeedId)[0];
    duplicatePositionObj.position.position_type_id = this.skillSetList.filter((skill) => skill.label === position.position.type)[0];
    this.positionMinDate = new Date(duplicatePositionObj.position.start_date);
    if (duplicatePositionObj.position.employee.id.id === 0) {
      duplicatePositionObj.position.employee.id = null;
    }
    duplicatePositionObj.position.id = undefined; // since we are going to create a new position by duplicating the selected one.

    this.loading = true;
    const saveEditPosition = {
      bill_rate: duplicatePositionObj.position.bill_rate === 'n/a' ? 0 : duplicatePositionObj.position.bill_rate,
      cost: duplicatePositionObj?.position?.cost || 0,
      daily_billable_hours: duplicatePositionObj.position.daily_billable_hours,
      employee_id: undefined,
      end_date: this.datePipe.transform(new Date(duplicatePositionObj.position.end_date), AppConstants.format),
      name: duplicatePositionObj.position.name + '(Copy)',
      position_type_id: duplicatePositionObj?.position?.position_type_id?.id,
      project_id: duplicatePositionObj?.position?.project?.id,
      start_date: this.datePipe.transform(new Date(duplicatePositionObj.position.start_date), AppConstants.format),
      id: undefined // since we are going to create a new position by duplicating the selected one.
    };
    this.subscriptionManager.add(
      this.utilizationService.updatePosition(saveEditPosition).subscribe(
        (res) => {
          this.duplicatedPositionObject = res?.data;
          // sometimes the duplicate call doesn't effect, and the listing gets back without the duplicated row
          this.editModeEnable = true;
          this.loading = false;
          this.loadReport(null, dt);
          this.positionMinDate = null;
          this.cdf.detectChanges();
          this.layoutUtilsService.showActionNotification('Position duplicated successfully', AlertType.Success);
        },
        () => {
          this.loading = false;
        }
      )
    );
  }

  // returns tag from the category sub category
  getExtractedTags(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 1];
  }

  getExtractedTagsFromSelectedTags(tags: string[]) {
    return tags.map((tag) => tag.split('__').pop() || '');
  }

  getTagsCount(tagWithCategory: string, allowedTagLength?: boolean): string {
    if (allowedTagLength) {
      const tagArray = tagWithCategory.split('__');
      return tagArray[tagArray.length - 1];
    }
  }

  getTagCount(tags: string[]): string {
    return '+ ' + (tags?.length - 2);
  }

  // returns tags parent category
  getExtractedTagsParentCategory(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 2];
  }

  getTagCategorySubCategory(tags: string): string {
    const tagArray = tags.split('__');
    const categoryName = tagArray[0];
    const subCategory = tagArray?.length > 2 ? tagArray[1] : null;
    if (subCategory) return `Category <strong>${categoryName}</strong> <br> Sub Category<strong>${subCategory}</strong>`;
    else return `Category <strong>${categoryName}</strong>`;
  }

  toggleWithCategory(tooltip, tag) {
    if (tooltip.isOpen()) {
      tooltip.close();
    } else {
      tooltip.open({ tag });
    }
  }

  openTagModal(tag) {
    this.showTagDialog = true;
    this.selectedTagToView = tag;
  }

  prepareCalendarWithProjectsAndAllocations(): Promise<void> {
    const redColor = '#FFC857';
    const greenColor = '#4b99a2';
    this.shifts = [];
    this.obj = {
      children: []
    };
    this.myEvents = [];
    return new Promise((resolve) => {
      let tempCalPosition: ResorceType;
      let tempCalPositionEvent: any;
      const tempResource = [];
      const tempEvents = [];
      for (const position of this.positionReportData) {
        tempCalPosition = {
          id: position.position.id,
          name: `${position.position.project?.name}`,
          customer: `${position.position?.project?.customer?.name}`,
          color: position.position.employee.first_name ? greenColor : redColor
        };

        tempCalPositionEvent = {
          title: position.position.name,
          start: position.position.start_date,
          end: position.position.end_date,
          employeeFirstName: position.position.employee.first_name,
          employeeLastName: position.position.employee.last_name,
          resource: position.position.id,
          color: position.position.employee.first_name ? greenColor : redColor
        };
        if (this.groupByClient) {
          this.shifts.push(tempCalPositionEvent);
          this.groupByCustmerName(position);
        } else if (this.groupByEmployee) {
          this.shifts.push(tempCalPositionEvent);
          this.groupByEmployeeName(position);
        } else if (this.groupByProject) {
          this.shifts.push(tempCalPositionEvent);
          this.groupByProjectName(position);
        } else {
          tempResource.push(tempCalPosition);
          tempEvents.push(tempCalPositionEvent);
          this.myEvents = tempEvents;
        }
      }
      this.myResources = tempResource;
      for (const property in this.obj) {
        if (property !== 'children') {
          this.myEvents.push({
            name: property,
            ...this.obj[property]
          });
        }
      }
      this.calendarOptions.resources = [...this.myEvents];
      this.cdf.detectChanges();
      resolve();
    });
  }

  groupByCustmerName(pos) {
    const customerName = pos.position.project.customer.name;
    if (this.obj[customerName]) {
      this.obj[customerName].children.push({
        name: pos.position.project.name,
        id: pos.position.project.id,
        ...pos.position
      });
    } else {
      this.obj[customerName] = { children: [] };
      this.obj[customerName].id = pos.position.project.customer.id;
    }
  }

  groupByEmployeeName(pos) {
    const employeeName = `${pos.position.employee.first_name}${pos.position.employee.last_name}`;

    if (!this.obj[employeeName]) {
      this.obj[employeeName] = {
        children: []
      };
    }

    this.obj[employeeName].children.push({
      name: `${pos.position.employee.first_name}${pos.position.employee.last_name}`,
      id: pos.position.id
    });
  }

  groupByProjectName(pos) {
    const employeeName = `${pos.position.project.name}`;

    if (!this.obj[employeeName]) {
      this.obj[employeeName] = {
        id: pos.position.employee.id,
        children: []
      };
    }

    this.obj[employeeName].children.push({
      name: `${pos.position.project.name}`,
      id: pos.position.id
    });
  }

  // used to update the event view
  pageSizeChangeHandler(pageViewType: string) {
    this.preparingCalendarData = true;
    switch (pageViewType) {
      case CalendarPageViewConfigType.year:
        this.timelineType = CalendarPageViewConfigType.year;
        this.myView = {
          timeline: {
            type: this.timelineType,
            size: 1,
            resolution: this.timelineResolution,
            eventList: true
          }
        };
        this.preparingCalendarData = false;
        break;
      case CalendarPageViewConfigType.month:
        this.timelineType = CalendarPageViewConfigType.month;
        this.myView = {
          timeline: {
            type: this.timelineType,
            size: 1,
            resolution: this.timelineResolution,
            eventList: true
          }
        };
        this.preparingCalendarData = false;
        break;
      case CalendarPageViewConfigType.week:
        this.timelineType = CalendarPageViewConfigType.week;
        this.myView = {
          timeline: {
            type: this.timelineType,
            size: 1,
            resolution: this.timelineResolution,
            eventList: true
          }
        };
        this.preparingCalendarData = false;
        break;
      case CalendarPageViewConfigType.day:
        this.timelineType = CalendarPageViewConfigType.day;
        this.myView = {
          timeline: {
            type: this.timelineType,
            size: 1,
            resolution: this.timelineResolution,
            eventList: true
          }
        };
        this.preparingCalendarData = false;
        break;
    }
    this.cdf.detectChanges();
  }

  pageResolutionHandler(pageViewType: string) {
    this.preparingCalendarData = true;
    setTimeout(() => {
      localStorage.setItem('calendarBreakDown', pageViewType);
    }, 0);
    switch (pageViewType) {
      case CalendarPageViewConfigType.year:
        this.timelineResolution = CalendarPageViewConfigType.year;
        this.myView = {
          timeline: {
            type: this.timelineType,
            size: 1,
            resolution: this.timelineResolution,
            eventList: true
          }
        };
        this.preparingCalendarData = false;
        break;
      case CalendarPageViewConfigType.month:
        this.timelineResolution = CalendarPageViewConfigType.month;
        this.myView = {
          timeline: {
            type: this.timelineType,
            size: 1,
            resolution: this.timelineResolution,
            eventList: true
          }
        };
        this.preparingCalendarData = false;
        break;
      case CalendarPageViewConfigType.week:
        this.timelineResolution = CalendarPageViewConfigType.week;
        this.myView = {
          timeline: {
            type: this.timelineType,
            size: 1,
            resolution: this.timelineResolution,
            eventList: true
          }
        };
        this.preparingCalendarData = false;
        break;
      case CalendarPageViewConfigType.day:
        this.timelineResolution = CalendarPageViewConfigType.day;
        this.myView = {
          timeline: {
            type: this.timelineType,
            size: 1,
            resolution: this.timelineResolution,
            eventList: true
          }
        };
        this.preparingCalendarData = false;
        break;
    }
    this.cdf.detectChanges();
  }

  mouseEnter(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  mouseLeave(): void {
    this.timer = setTimeout(() => {
      this.tooltip.close();
    }, 200);
  }

  addFinicalColumn(): Array<any> {
    const financialData = [
      { dataKey: 'revenue', title: 'Revenue', bydefaultSelected: false },
      { dataKey: 'margins', title: 'Margins', bydefaultSelected: false }
    ];
    return financialData;
  }

  checkSelectedColumn(componentType: ComponentsType, key: string): boolean {
    const finalKey = `${componentType}: ${key?.replace(/\s/g, '_')?.trim()}`.trim() || '';
    return this._pCols.includes(finalKey);
  }

  extractNames(data: any, componentName: string): string[] {
    return data
      ?.map((item) => {
        const projectConfig = item?.jsonData?.extendedFieldsConfig?.find((config) => config?.component === componentName);
        return projectConfig ? projectConfig?.fields.map((field) => field?.name?.trim()) : [];
      })
      .flat(2);
  }

  extractExtendFlow(componentType: ComponentsType): Array<any> {
    let extendFiled = [];
    let prepareKey = this.extractNames(this.extendFields, componentType);
    for (const key of prepareKey) {
      const keyPreProcess = `${componentType}: ${key?.replace(/\s/g, '_').trim()}`.trim();
      extendFiled.push({
        dataKey: keyPreProcess || '',
        title: `${componentType}: ${key?.toUpperCase()}`,
        bydefaultSelected: false
      });
    }
    return extendFiled;
  }

  getGlobalDetailsCategory(): void {
    this.subscriptionManager.add(
      this.adminService.getExtendedFields('ManageExtendedFiled').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'ManageExtendedFiled') {
              this.extendFields = res.data.global_details[0].global_detail.extended_fields.extendArray || [];
              this.frozenCols = [
                ...this.frozenCols,
                ...this.extractExtendFlow(ComponentsType.Position),
                ...this.extractExtendFlow(ComponentsType.Client),
                ...this.extractExtendFlow(ComponentsType.Employee),
                ...this.extractExtendFlow(ComponentsType.Project)
              ];
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getValueByDBTag(dbTag: string, extendFieldsObj: any = {}, fieldType?: string): string {
    if (fieldType === this.filedType.MultiDropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      const value = Array.isArray(extendFieldsObj[dbTag]) ? extendFieldsObj[dbTag]?.map((item) => item?.name).join(', ') : '';
      return value;
    }
    if (fieldType === this.filedType.Dropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag].name : '';
    }
    return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag] : '';
  }

  updateObjectByPartialKey(partialKey: string, value: any): void {
    const fullKey = `ext_${partialKey?.toLowerCase()?.replace(/\s/g, '_')?.trim()}`;
    this.extendFieldsObj[fullKey] = value || '';
  }

  openExtendFiledPopup(positionObj: Position, fieldDetail: extendedField, isEditLink = false): void {
    this.fieldDetail = fieldDetail;
    this.linkValue = this.getValueByDBTag(fieldDetail?.DBTag, positionObj?.position?.extended_fields) || '';
    if (fieldDetail?.type === this.filedType.Hyperlink && this.linkValue && !isEditLink) {
      this.openHyperlinkPopup(positionObj, fieldDetail?.name);
      return;
    }
    this.updateExtendFiled = fieldDetail?.name;
    this.showUpdateExtendFiledDialog = true;
    this.positionObj = positionObj;
    this.positionSetupForm = JSON.parse(JSON.stringify(this.positionObj));
    this.editPositionObj = positionObj;
    this.showHyperlinkNavigationDialog = false;
  }

  openHyperlinkPopup(positionObj: Position, fieldName?: string): void {
    this.showHyperlinkNavigationDialog = true;
    this.updateExtendFiled = fieldName;
    this.positionObj = positionObj;
  }

  closeExtendFiledPopup(): void {
    this.positionReportData = this.positionReportData.map((pos) => {
      if (pos.position?.id === this.positionObj?.position?.id) {
        return {
          position: {
            ...pos.position,
            extended_fields: {
              ...this.positionSetupForm?.position?.extended_fields
            }
          }
        };
      }
      return pos;
    });

    this.updateExtendFiled = '';
    this.showUpdateExtendFiledDialog = false;
    this.positionObj = {};
    this.fieldDetail = null;
    this.extendedFieldFormComponent?.onReset();
  }

  handelManageProject(): void {
    setTimeout(() => {
      this.activatedRoute.queryParams.subscribe((params) => {
        if (params['id']) {
          this.filterReport();
          this.openFilter = false;
        }
      });
    }, 300);
  }

  getValueBYExtendFiled(componentsType: ComponentsType, object: OpenPositionReport, dbTag: string, fieldType?: string): string {
    let extendedOBJ = {};
    switch (componentsType) {
      case ComponentsType.Position:
        extendedOBJ = object?.position?.extended_fields || {};
        break;
      case ComponentsType.Project:
        extendedOBJ = object?.position?.project?.extended_fields || {};
        break;
      case ComponentsType.Client:
        extendedOBJ = object?.position?.project?.customer?.extended_fields || {};
        break;
      case ComponentsType.Employee:
        extendedOBJ = object?.position?.employee?.extended_fields || {};
        break;
    }
    return this.getValueByDBTag(dbTag, extendedOBJ, fieldType);
  }

  addExtendFiledTOReport(): any {
    let finalExtendFiled = [];
    for (const data of this.positionReportData) {
      let finalData = this.processObj(data);
      finalExtendFiled.push(finalData);
    }
    return finalExtendFiled;
  }

  processObj(data: any): any {
    let final = this.extendFields
      ?.map((item) => {
        return item?.jsonData?.extendedFieldsConfig?.map((filed) => {
          return filed?.fields?.map((filedDetails) => {
            let dateColumns = {};
            const filedName = `${filed?.component}: ${filedDetails?.name?.replace(/\s/g, '_')?.trim()}`?.trim();
            if (this._pCols?.includes(filedName)) {
              dateColumns[filedName] = this.getValueBYExtendFiled(filed?.component, data, filedDetails?.DBTag);
            }
            return { ...dateColumns };
          });
        });
      })
      ?.flat();
    return final?.flat();
  }

  editValueChange(editPositionObj: Position): void {
    editPositionObj.position.daily_billable_hours = editPositionObj?.position?.weekly_billable_hours ? +(editPositionObj.position.weekly_billable_hours / 5).toFixed(2) : 0;
  }

  editDailyBillableHours(editPositionObj: Position): void {
    editPositionObj.position.weekly_billable_hours = editPositionObj?.position?.daily_billable_hours ? +(editPositionObj.position.daily_billable_hours * 5).toFixed(2) : 0;
  }

  getColumnData(monthlyPositions: ValidatedMonthlyPositions[], month: number, year: string, field: string, defaultReturn = '--'): string | null {
    const monthlyPosition = monthlyPositions?.find(
      (position) => position?.validated_monthly_position?.month === month && position?.validated_monthly_position?.year === parseInt(year)
    );

    if (!monthlyPosition) return defaultReturn;

    if (field === 'margins') {
      const margin = Number(monthlyPosition?.validated_monthly_position?.percent_gross_margin) * 100;
      return margin ? margin.toFixed(2) : defaultReturn;
    }

    if (field === 'monthly_Variance' && monthlyPosition) {
      const { billable_hours, actual_hours } = monthlyPosition?.validated_monthly_position;
      const variance = +billable_hours - +actual_hours;
      return variance ? variance.toFixed(2) : defaultReturn;
    }

    const fieldValue = monthlyPosition?.validated_monthly_position[field];
    return fieldValue ? this.commaNumberPipe.transform(fieldValue) : defaultReturn;
  }

  checkVarianceIsZero(data: any): string {
    return data?.position?.total?.billable_hours - data?.position?.total?.actual_hours != 0 ? this.appConstants.statusMessage.danger : this.appConstants.statusMessage.success;
  }

  checkVarianceMonthVise(data: any, col: any): string {
    return Number(this.getColumnData(data?.position?.validated_monthly_positions, col.month, col.year, this.appConstants.columnName.monthly_Variance, '0.0')) != 0
      ? this.appConstants.statusMessage.danger
      : this.appConstants.statusMessage.success;
  }

  showTotalVarianceToolTip(data: any): string {
    const totalActualHours = data?.position?.total?.actual_hours || 0;
    const totalProjectHours = data?.position?.total?.billable_hours || 0;

    return `${this.appConstants.tooltipText.total} ${this.appConstants.tooltipText.actualHours}: ${totalActualHours}
            ${this.appConstants.tooltipText.total}  ${this.appConstants.tooltipText.projectHours}: ${totalProjectHours}`;
  }

  showMonthlyVarianceToolTip(data: any, col: any): string {
    const monthlyPosition = data?.position?.validated_monthly_positions?.find(
      (position) => position?.validated_monthly_position?.month === col.month && position?.validated_monthly_position?.year === parseInt(col.year)
    );

    const actualHours = monthlyPosition?.validated_monthly_position?.actual_hours || 0;
    const projectHours = monthlyPosition?.validated_monthly_position?.billable_hours || 0;

    return `${this.appConstants.tooltipText.actualHours} : ${actualHours}
            ${this.appConstants.tooltipText.projectHours} : ${projectHours}`;
  }

  onPageChange(event: any, isCalendarView = false): void {
    this.page = event.first;
    this.pageSize = event.rows;
    this.dataFilter.offset = this.page;
    this.dataFilter.limit = this.pageSize;
    if (isCalendarView) {
      this.loadReport(event);
    }
  }

  onSort(event: any): void {
    this.sortFieldName = event.field;
    this.sortOrderNumber = event.order;
  }

  resetTableSort(): void {
    (this.sortFieldName = 'name'), (this.sortOrderNumber = 1);
  }

  resetReport(): void {
    this.resetTablePagination();
    this.loadReport();
  }

  resetTablePagination(): void {
    this.page = this.dataFilter.offset = 0;
    this.pageSize = this.dataFilter.limit = 10;
  }

  resetTableSortAndPagination(): void {
    this.resetTablePagination();
    this.resetTableSort();
  }

  onReset(event: any): void {
    this.resetReport();
  }

  lazyLoadTable(event: any): void {
    this.onSort(event);
    this.loadReport(event);
  }

  downloadReportInBackground(duplicateQueryFilter: any): void {
    this.downloadButtonDisable = true;
    this.subscriptionManager.add(
      this.utilizationService.getOpenPositionReport(duplicateQueryFilter).subscribe(
        async (res) => {
          this.positionExportData = res.body.data?.positions ? res.body.data?.positions : [];
          this.positionExportData?.forEach((pos) => {
            pos.position.project.status = this.projectList.find((x) => x.value == pos.position.project.id)?.status;
          });
          this.positionExportData.forEach((pos) => {
            pos.position.employee.employeeFullName = pos.position.employee.first_name ? pos.position.employee.first_name + ' ' + pos.position.employee.last_name : '--Open--';
            pos.position.weekly_billable_hours = (+pos?.position?.daily_billable_hours * 5)?.toString();
          });
          this.prepareHeaders();
          this.loading = false;
          this.loadingReport = false;
        },
        () => {
          this.loading = false;
          this.loadingReport = false;
        }
      )
    );
  }

  clearSkillSet(): void {
    this.selectedSkillSetObj = null;
    this.dataFilter.position_type_id = null;
  }

  getNavigationLink(link: string): string {
    if (!link) return '';
    return this.appConstants.regexForHyperlink.test(link) ? link : `https://${link}`;
  }

  getValidLink(componentsType: ComponentsType, fieldObject: OpenPositionReport, dbTag: string): string {
    const link = this.getValueBYExtendFiled(componentsType, fieldObject, dbTag);
    if (!link) return '';
    return this.appConstants.regexForHyperlink.test(link) ? link : `https://${link}`;
  }

  checkExtendedFormValidity(): boolean {
    const isValid = this.extendedFieldFormComponent?.isFormValid() || false;
    return !isValid;
  }

  navigateOnLink(): void {
    this.showHyperlinkNavigationDialog = false;
    this.fieldDetail = null;
    if (this.linkValue) {
      const hyperLink = this.getNavigationLink(this.linkValue);
      window.open(hyperLink, '_blank');
      this.linkValue = '';
    }
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    if (this.openFilter) {
      if (event.key === 'Escape') {
        event.preventDefault();
        this.onClose(true);
      } else if (event.key === 'Enter') {
        event.preventDefault();
        this.filterReport();
      }
    }
  }
}
