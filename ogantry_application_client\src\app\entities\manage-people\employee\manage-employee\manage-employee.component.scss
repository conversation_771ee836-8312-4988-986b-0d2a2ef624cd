@import '/src/assets/sass/components/variables.bootstrap';

#manageEmployeeScreen {
  ::ng-deep .p-dropdown .p-dropdown-trigger {
    width: 1.2rem;
  }
  ::ng-deep .p-dropdown {
    width: 100%;
    border: 1px solid #4b3f72 !important;
  }
  ::ng-deep .p-inputtext {
    width: 100%;
    border: 1px solid #4b3f72 !important;
  }

  ::ng-deep .p-dropdown.p-focus {
    box-shadow: 0 0 0 0.1rem #4b3f72 !important;
  }

  ::ng-deep .p-dropdown-label {
    padding-right: 0rem !important;
    line-height: inherit !important;
  }

  ::ng-deep .p-dropdown .p-inputtext {
    border: 0 !important;
  }

  .my-custom-class {
    color: #9b9b9b;
    font-family: Poppins;
    font-size: 10px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 16px;
  }
  .icon-style {
    color: #b5b5c3;
  }

  .header-width {
    width: 16%;
  }

  .icon-background {
    background-color: #4b3f72 !important;
    padding: 0.2rem;
  }

  ::ng-deep .select-column-wrapper .dropdown .p-dropdown,
  ::ng-deep .select-column-wrapper .p-multiselect {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 30px !important;
  }

  ::ng-deep .select-column-wrapper .dropdown .p-dropdown,
  ::ng-deep .select-column-wrapper .dropdown .p-dropdown .p-focus {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    box-shadow: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
    padding: 1.2rem;
  }

  ::ng-deep .select-column-wrapper .dropdown .p-dropdown,
  ::ng-deep .select-column-wrapper .p-multiselect {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
    padding: 1.2rem;
    max-width: 250px;
  }

  ::ng-deep .select-column-wrapper.dropdown .p-dropdown .p-dropdown-label,
  ::ng-deep .select-column-wrapper .p-multiselect .p-multiselect-label {
    color: #000000;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: -0.32px;
    line-height: 25px;
    padding-top: 0.5rem;
    padding-left: 1rem;
  }

  ::ng-deep .select-column-wrapper .dropdown .p-dropdown .p-dropdown-label {
    color: #000000;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: -0.32px;
    line-height: 25px;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
    background: #e9ecef;
    border-color: transparent;
    color: #495057;
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight) {
    border-radius: 50%;
    background-color: #f4f5f8;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page {
    font-size: 14px;
    &.p-highlight {
      background: #4b3f72;
      border-color: #e3f2fd;
      color: #ffffff;
      border-radius: 50%;
      pointer-events: none;
    }
  }

  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    height: 49px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    padding: 0.5rem 0.5rem;
    background-color: #ecedf6 !important;
    width: 130px;
  }

  ::ng-deep .p-datatable .p-datatable-thead tr > th:first-child,
  ::ng-deep .p-datatable .p-datatable-tbody tr > td:first-child {
    padding-left: 1rem;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    height: 48px;
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 0.5rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  ::ng-deep .pi-icon .pi {
    font-size: 0.5rem;
  }

  ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
    fill: white !important;
  }

  ::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
    border-color: #827da0 !important;
    background: #827da0 !important;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight,
  .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column:focus {
    box-shadow: inset 0 0 0 0.2rem #4b3f72;
    outline: 0 none;
  }
  ::ng-deep .p-paginator .p-dropdown {
    width: 91px;
  }

  ::ng-deep .p-datatable-tbody tr:last-child > td {
    border-bottom: none;
  }
  .search-tag-wrapper {
    padding: 0rem;
    background-color: #ffffff !important;
    border-radius: 5px !important;
    width: 100%;
  }

  ::ng-deep .search-tag-wrapper .p-treeselect-panel {
    .p-element {
      .p-tree {
        min-width: 250px !important;
        max-height: 50vh !important;
        overflow-y: auto;
        top: 200px !important;
        position: fixed;
        z-index: 1004 !important;
        box-shadow: 0 2px 4px -1px rgb(0 0 0 / 20%), 0 4px 5px 0 rgb(0 0 0 / 14%), 0 1px 10px 0 rgb(0 0 0 / 12%);
        border-radius: 3px;
      }
    }
  }

  ::ng-deep {
    .p-treeselect {
      width: 100%;
      border: 1px solid #4b3f72 !important;
    }
  }

  ::ng-deep {
    span.tag-count p-badge span {
      background-color: #4b3f72;
    }
    .p-datatable-table {
      position: relative;
    }
  }

  ::ng-deep .p-paginator {
    display: flex !important;
    justify-content: flex-start !important;
    border: none;
    margin-top: 15px;

    .p-paginator-current {
      position: absolute;
      right: 0;
      color: #575962;
      font-size: 14px;
      letter-spacing: 0;
      font-weight: 500;
      cursor: default;
    }

    .p-paginator-rpp-options {
      margin-left: 20px;
      height: 37px;
      width: 100px;
      border-radius: 20px;
      background-color: #f4f5f8;
      border: none;
    }
    .p-dropdown-label.p-inputtext {
      display: flex;
      align-items: center;
      padding-left: 15px;
      font-size: 14px;
    }

    .p-dropdown-trigger-icon {
      padding-right: 20px;
    }
  }

  .show-pointer {
    cursor: pointer;
    color: #4b3f72 !important;
    text-decoration: underline;
  }

  .no-wrap {
    text-wrap: nowrap;
  }

  @media (max-width: 500px) {
    ::ng-deep .p-datatable table {
      display: block;
      overflow: auto;
      white-space: nowrap;
    }

    ::ng-deep .p-paginator-current {
      right: 32px;
      bottom: 10px;
    }

    ::ng-deep .p-paginator-rpp-options {
      margin: 20px 0 0 0 !important;
    }
  }

  ::ng-deep .p-inputtext:enabled:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #4b3f72;
    border-color: #4b3f72;
  }

  ::ng-deep .p-inputtext:enabled:hover {
    border-color: #4b3f72;
  }
  .badge {
    height: 20px;
    width: 56px;
    color: #dc2f44;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
    text-align: center;
  }

  .badge-active {
    color: #109aa1 !important;
  }

  .badge-inactive {
    color: #dc2f44;
  }

  .center-align {
    text-align: center !important;
  }

  ::ng-deep .p-datatable .p-datatable-loading-overlay {
    top: 80px;
    background-color: transparent;
  }

  ::ng-deep .p-calendar .p-datepicker {
    position: absolute;
    width: 400px;
  }
  ::ng-deep .p-calendar .p-inputtext {
    width: 100%;
    height: inherit !important;
    border-radius: 3px;
  }
  ::ng-deep .p-calendar {
    width: 100%;
  }

  ::ng-deep .p-datatable-responsive-scroll > .p-datatable-wrapper {
    overflow-x: initial !important;
    min-height: 55vh !important;
  }

  ::ng-deep .p-input-icon-right > em:last-of-type {
    color: #8a9191;
    font-size: 0.5rem;
    right: 0.5rem;
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
  }
  .text-number-right {
    text-align: right;
  }

  .header-width-actions {
    max-width: 130px;
  }

  .header-width-region {
    .p-element {
      width: 100%;
    }
  }

  .heading {
    padding: 0.5rem;
  }
  .display {
    display: inline-block;
  }
  .pr-3,
  .px-3 {
    padding-right: 0.5rem !important;
  }

  ::ng-deep .future-financial-tooltip {
    .tooltip-inner {
      padding: 0px !important;
    }
    .tooltip-content {
      background: #4b3f72 !important;
      white-space: normal;
      color: #ffffff;
      padding: 0.5rem 0.5rem;
      box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
      border-radius: 3px;
      font-weight: 500;
    }
    .arrow::before {
      border-top-color: #4b3f72 !important;
    }
  }
}
::ng-deep .confirm-dialog .p-dialog {
  width: 24vw;
}

::ng-deep .confirm-dialog .p-dialog-title {
  color: #000000;
  font-family: Poppins;
  font-size: 16px !important;
  font-weight: 500 !important;
  letter-spacing: 0;
  line-height: 25px;
}
.delete-title {
  color: #050505;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.32px;
  line-height: 25px;
}

.action-icons a {
  margin: 0 3px;
}

::ng-deep .p-datatable-responsive-scroll > .p-datatable-wrapper {
  overflow-x: initial !important;
}

.sticky-row-1 > th {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 99;
}
.sticky-row-2 > th {
  position: -webkit-sticky;
  position: sticky;
  top: 40px;
  z-index: 99;
}

::ng-deep .dialog-applied-tags .p-dialog {
  width: 460px;
}

::ng-deep .cursor-pointer .tag-count .p-badge {
  background-color: $primary;
}
.checkbox-wrapper {
  display: flex;
  height: 49px !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.table-checkbox {
  display: flex;
  align-items: center;
  max-width: 42px !important;
  height: 48px !important;
}

.info-icon {
  color: #4b3f72;
  font-weight: 700;
  font-size: 14px;
}

::ng-deep .filter-dialog-manage-employee {
  .p-component-overlay {
    background: none !important;
    animation: none !important;
  }

  .p-dialog {
    height: auto !important;
    max-height: calc(100vh - 90px) !important;
    width: 400px;
    top: 51px;

    .p-dialog-header {
      display: none;
    }

    .p-dialog-content {
      padding-top: 1rem;
    }

    .title {
      color: #757575;
      font-family: Poppins;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 18px;
    }

    .share-icon {
      background-color: #4b3f72 !important;
      border-color: #4b3f72 !important;

      i,
      em {
        color: white;
      }
    }

    .filter-body {
      display: flex;
      align-content: center;
      align-items: center;
    }

    .form-check {
      height: 40px;
      background-color: white;
      display: flex;
      align-items: center;
      width: 100%;

      .form-check-label,
      .form-check-label:hover {
        cursor: pointer;
        color: black;
        width: 75%;
        color: #000000;
        font-family: Poppins;
        font-size: 12px;
        letter-spacing: 0;
        line-height: 18px;
      }

      .div {
        width: 30%;
        display: flex;
        justify-content: flex-end;
      }
    }

    .filter-icons {
      display: flex;
      width: 100%;
      justify-content: flex-end;
    }
  }
}

::ng-deep .popup-column {
  z-index: 100 !important;
  position: absolute !important;
  right: 5px !important;
  top: 100px !important;
}
::ng-deep .plus-icon {
  svg {
    fill: $primary;
  }
}
